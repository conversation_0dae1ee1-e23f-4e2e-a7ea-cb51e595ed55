"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/teams",{

/***/ "(pages-dir-browser)/./core/Teams/TeamTSkills.js":
/*!***********************************!*\
  !*** ./core/Teams/TeamTSkills.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @core/components/ui/card */ \"(pages-dir-browser)/./core/components/ui/card.jsx\");\n\n\nconst TeamTSkills = (param)=>{\n    let { technical_skills, role_ids } = param;\n    console.log(\"technical_skills\");\n    console.log(technical_skills);\n    console.log(\"role_ids\");\n    console.log(role_ids);\n    /*\n\n    - role ids for the selected team (extract and save in array)\n    - query role mapping for the role ids (multi query)\n    - get the technical skills for the role mappings\n\n\n ----- \n\n */ const skills_header = (technical_skills, role_ids)=>{\n        return technical_skills.map((skill)=>{\n            const hasMatchingRole = skill.role_mapping.some((mapping)=>role_ids.some((role)=>role.id === mapping.role_id));\n            return hasMatchingRole ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                className: \"min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                    className: \"font-semibold text-xs text-center p-1\",\n                    children: skill.skill_name\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, skill.id, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, undefined) : null;\n        });\n    };\n    const skill_rows = (technical_skills, role_ids)=>{\n        return technical_skills.map((skill)=>{\n            const hasMatchingRole = skill.role_mapping.some((mapping)=>role_ids.some((role)=>role.id === mapping.role_id));\n            return hasMatchingRole ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"font-bold text-sm text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block align-middle\",\n                                children: role_ids[0].role_name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"text-xs text-center tracking-tight p-0\",\n                            children: \"Independent Practitioner\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true) : null;\n        });\n    };\n    const skill_rows2 = (technical_skills, role_ids)=>{\n        role_ids.map((role, index)=>{\n            console.log(role);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"font-bold text-sm text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block align-middle\",\n                                children: role.role_name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"text-xs text-center tracking-tight p-0\",\n                            children: \"Independent Practitioner\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true);\n        });\n    // return technical_skills.map((skill) => {\n    //   const hasMatchingRole = skill.role_mapping.some((mapping) =>\n    //     role_ids.some((role) => role.id === mapping.role_id)\n    //   );\n    //   return hasMatchingRole ? (\n    //     <>\n    //       <Card className=\"min-w-8  bg-[#1f144a] text-primary-foreground\">\n    //         <CardContent className={\"font-bold text-sm text-center\"}>\n    //           <div className=\"inline-block align-middle\">\n    //             {role_ids[0].role_name}\n    //           </div>\n    //         </CardContent>\n    //       </Card>\n    //       <Card className=\"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\">\n    //         <CardContent className=\"text-xs text-center tracking-tight p-0\">\n    //           Independent Practitioner\n    //         </CardContent>\n    //       </Card>\n    //     </>\n    //   ) : null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                className: \"min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                    className: \"font-extrabold text-lg text-center\",\n                    children: \"Skills\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            skills_header(technical_skills, role_ids),\n            skill_rows2(technical_skills, role_ids)\n        ]\n    }, void 0, true);\n//   const getLevel = (lvl) => {\n//     switch (lvl) {\n//       case 1:\n//         return \"Operational Contributor\";\n//       case 2:\n//         return \"Advanced Contributor\";\n//       case 3:\n//         return \"Team Leader\";\n//       case 4:\n//         return \"Leader of Leaders\";\n//       case 5:\n//         return \"Organisational Leader\";\n//     }\n//   };\n//   return (\n//     <>\n//       {\" \"}\n//       <Card className=\"min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-extrabold text-lg text-center\"}>\n//           Skills\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           BCDR\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Change Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Certification Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold  text-xs text-center p-1\"}>\n//           Compliance & Regulatory Assurance\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold  text-xs text-center p-1\"}>\n//           Data Analytics and Insights\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Development Lifecycle\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Incident Response Lifecycle\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Infrastructure and Cloud Computing\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold  text-xs text-center p-1\"}>\n//           Procurement\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Risk Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Supplier Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Threat Intelligence\n//         </CardContent>\n//       </Card>\n//     </>\n//   );\n//     filteredBSkills &&\n//     filteredBSkills.map((role) => {\n//       if (role.role_name === \"Team Owner\") {\n//         return null;\n//       }\n//       return (\n//         <>\n//           <Card className=\"min-w-8  bg-[#1f144a] text-primary-foreground \">\n//             <CardContent className={\"font-semibold text-sm text-center\"}>\n//               <div className=\"inline-block align-middle\">{role.role_name}</div>\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight] \">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//         </>\n//       );\n//     })\n//   );\n};\n_c = TeamTSkills;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TeamTSkills);\nvar _c;\n$RefreshReg$(_c, \"TeamTSkills\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/Teams/TeamTSkills.js\n"));

/***/ })

});