"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./pages/dashboard.js":
/*!****************************!*\
  !*** ./pages/dashboard.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(pages-dir-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/separator */ \"(pages-dir-browser)/./core/components/ui/separator.jsx\");\n/* harmony import */ var _core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/ui/card */ \"(pages-dir-browser)/./core/components/ui/card.jsx\");\n/* harmony import */ var _core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/accordion */ \"(pages-dir-browser)/./core/components/ui/accordion.jsx\");\n/* harmony import */ var _core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/carousel */ \"(pages-dir-browser)/./core/components/ui/carousel.jsx\");\n/* harmony import */ var _core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/components/ui/table */ \"(pages-dir-browser)/./core/components/ui/table.jsx\");\n/* harmony import */ var _core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/tabs */ \"(pages-dir-browser)/./core/components/ui/tabs.jsx\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_badge__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/badge */ \"(pages-dir-browser)/./core/components/ui/badge.jsx\");\n/* harmony import */ var _public_skills_chart_png__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../../../../../../../../public/skills-chart.png */ \"(pages-dir-browser)/./public/skills-chart.png\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var _core_Dashboard_TechSkillSummary__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @core/Dashboard/TechSkillSummary */ \"(pages-dir-browser)/./core/Dashboard/TechSkillSummary.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills, tech_data_user } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userBLevel, setUserBLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userBLevelDisplay, setUserBLevelDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userTLevel, setUserTLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userTLevelDisplay, setUserTLevelDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRoleDesc, setUserRoleDesc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filteredTechSkills, setFilteredTechSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBSkills, setShowBSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTSkills, setShowTSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showTabView, setShowTabView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [carouselApi, setCarouselApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsThink, setTotalItemsThink] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsExecute, setTotalItemsExecute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsGrow, setTotalItemsGrow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [skillsLevel1, setSkillsLevel1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel2, setSkillsLevel2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel3, setSkillsLevel3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel4, setSkillsLevel4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!carouselApi) return;\n            const updateCarouselState = {\n                \"Dashboard.useEffect.updateCarouselState\": ()=>{\n                    setCurrentIndex(carouselApi.selectedScrollSnap());\n                    setTotalItems(carouselApi.scrollSnapList().length);\n                }\n            }[\"Dashboard.useEffect.updateCarouselState\"];\n            updateCarouselState();\n            carouselApi.on(\"select\", updateCarouselState);\n            return ({\n                \"Dashboard.useEffect\": ()=>{\n                    carouselApi.off(\"select\", updateCarouselState); // Clean up on unmount\n                }\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        carouselApi\n    ]);\n    const scrollToIndex = (index)=>{\n        carouselApi === null || carouselApi === void 0 ? void 0 : carouselApi.scrollTo(index);\n    };\n    const handleShowSkills = (value)=>{\n        if (value == \"Behavioural\") {\n            setShowTSkills(false);\n            setShowBSkills(true);\n            setShowTabView(true);\n        }\n        if (value == \"Technical\") {\n            setShowBSkills(false);\n            setShowTSkills(true);\n            setShowTabView(false);\n        }\n    };\n    /* --- DEBUG --- */ // console.log(userData);\n    // console.log(\"filteredTechSkills\");\n    // console.log(filteredTechSkills);\n    // console.log(\"userRole\");\n    // console.log(userRole);\n    // console.log(\"tech_data_user\");\n    // console.log(tech_data_user);\n    // console.log(\"filteredTechData\");\n    // console.log(filteredTechData);\n    // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    // filter the array based on user role\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n                // const filteredTech = tech_data_user.filter(\n                //   (el) =>\n                //     el.role_mapping.filter((n) => n.role_id === data.role).length > 0\n                // );\n                // better perfomance / dont need to filter all\n                const filteredTech = tech_data_user.filter((param)=>{\n                    let { role_mapping } = param;\n                    return role_mapping.some((n)=>n.role_id === data.role);\n                });\n                const skills1 = tech_data_user.filter((param)=>{\n                    let { role_mapping } = param;\n                    return role_mapping.some((n)=>n.role_id === data.role && n.skill_level === 1);\n                });\n                setSkillsLevel1(skills1.length);\n                const skills2 = tech_data_user.filter((param)=>{\n                    let { role_mapping } = param;\n                    return role_mapping.some((n)=>n.role_id === data.role && n.skill_level === 2);\n                });\n                setSkillsLevel2(skills2.length);\n                const skills3 = tech_data_user.filter((param)=>{\n                    let { role_mapping } = param;\n                    return role_mapping.some((n)=>n.role_id === data.role && n.skill_level === 3);\n                });\n                setSkillsLevel3(skills3.length);\n                const skills4 = tech_data_user.filter((param)=>{\n                    let { role_mapping } = param;\n                    return role_mapping.some((n)=>n.role_id === data.role && n.skill_level === 4);\n                });\n                setSkillsLevel4(skills4.length);\n                setFilteredTechSkills(filteredTech);\n                getUserRole(data.role);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get user's role\n    async function getUserRole(role_id) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name, behavioural_skill_level, role_description\").eq(\"id\", role_id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRole(data);\n                getUserBLevel(data.behavioural_skill_level);\n                setUserRoleDesc(data.role_description);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // set level for b skills\n    const getUserBLevel = (level)=>{\n        switch(level){\n            case 1:\n                setUserBLevel(\"Operational Contributor\");\n                setUserBLevelDisplay(\"level_1_description\");\n                break;\n            case 2:\n                setUserBLevel(\"Advanced Contributor\");\n                setUserBLevelDisplay(\"level_2_description\");\n                break;\n            case 3:\n                setUserBLevel(\"Team Leader\");\n                setUserBLevelDisplay(\"level_3_description\");\n                break;\n            case 4:\n                setUserBLevel(\"Leader of Leaders\");\n                setUserBLevelDisplay(\"level_4_description\");\n                break;\n            case 5:\n                setUserBLevel(\"Organisational Leader\");\n                setUserBLevelDisplay(\"level_5_description\");\n                break;\n        }\n    };\n    // set level for b skills\n    const getUserTLevel = (level)=>{\n        switch(level){\n            case 1:\n                // setUserBLevel(\"Knowledgablee\");\n                return \"level_1_description\";\n                break;\n            case 2:\n                // setUserBLevel(\"Supported Practitioner\");\n                return \"level_2_description\";\n                break;\n            case 3:\n                // setUserBLevel(\"Independent Practitioner\");\n                return \"level_3_description\";\n                break;\n            case 4:\n                // setUserBLevel(\"Expert\");\n                return \"level_4_description\";\n                break;\n        }\n    };\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 359,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                    lineNumber: 358,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n            lineNumber: 356,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_17__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this),\n                                showBSkills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Skills by Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbLink, {\n                                                    children: \"Behavioural\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreadcrumbPage, {\n                                                    children: \"Cyber Security Operations Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                        lineNumber: 386,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                    lineNumber: 385,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_16__.BreadcrumbLink, {\n                                                    children: \"My Skills Snapshot\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.Tabs, {\n                        defaultValue: \"snapshot\",\n                        className: \"relative mr-auto w-full mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsList, {\n                                className: \"w-full justify-start rounded-none border-b bg-transparent p-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                        value: \"snapshot\",\n                                        className: \"relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-dccpink data-[state=active]:text-foreground data-[state=active]:shadow-none \",\n                                        children: \"Snapshot\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                        value: \"behavioural\",\n                                        className: \"relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-dccpink data-[state=active]:text-foreground data-[state=active]:shadow-none \",\n                                        children: \"Behavioural Skills\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                        value: \"technical\",\n                                        className: \"relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-dccpink data-[state=active]:text-foreground data-[state=active]:shadow-none \",\n                                        children: \"Technical Skills\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                value: \"snapshot\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 ml-3 col-span-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                        className: \"rounded-lg max-w-2xl pb-1 pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                    className: \"text-lg text-gray-700\",\n                                                                    children: \"Behavioural Skills\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    className: \"pb-0 ml-0\",\n                                                                    src: _public_skills_chart_png__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                                    // width={150}\n                                                                    // height={250}\n                                                                    alt: \"DCC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                        className: \"rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                className: \"text-lg text-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                    children: userRole === null || userRole === void 0 ? void 0 : userRole.role_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                className: \"pb-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                                                        className: \"h-18\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                                                        className: \"w-[100px]\",\n                                                                                        children: \"Behavioural Level:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                        lineNumber: 471,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_badge__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                                                                            className: \"bg-dccblue\",\n                                                                                            children: userBLevel\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 475,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                        lineNumber: 474,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-semibold pt-6\",\n                                                                        children: [\n                                                                            \" \",\n                                                                            \"Role description:\",\n                                                                            \" \"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm pt-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"pb-7\",\n                                                                            children: userRoleDesc\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"pt-3 pl-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                                            className: \"mr-2 ml-1 bg-[#5c2071] hover:bg-[#5c2071]\",\n                                                                            children: \"Download job description\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                            lineNumber: 488,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this),\n                                        showTSkills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            className: \"rounded-lg m-3 p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                        className: \"text-lg text-gray-700\",\n                                                        children: \"Technical Skills\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Dashboard_TechSkillSummary__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        filteredTechSkills: filteredTechSkills,\n                                                        userRole: userRole && userRole.id\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                value: \"behavioural\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pl-3 pr-3 pt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-8 bg-dccpink text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Think\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: behavioural_skills.map((skill, index)=>{\n                                                                if (skill.group === \"Think\") {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: behavioural_skills.length - 1 == index + 1 ? \"bg-dccpink text-white font-bold p-3 hover:bg-dccpink/85 rounded-b rounded-t-none min-w-78\" : \"bg-dccpink text-white font-bold p-3 hover:bg-dccpink/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: skill.skill_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 539,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.behavioural_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 561,\n                                                                                                                            columnNumber: 47\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 560,\n                                                                                                                        columnNumber: 45\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[userBLevelDisplay]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 566,\n                                                                                                                            columnNumber: 47\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 565,\n                                                                                                                        columnNumber: 45\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                lineNumber: 559,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                            lineNumber: 558,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 555,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 575,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 576,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 550,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItemsThink\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 583,\n                                                                                                    columnNumber: 39\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 580,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                    lineNumber: 549,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 548,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 517,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 pt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                            className: \"min-w-8 bg-dccviolet text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                children: \"Execute\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                        type: \"single\",\n                                                        collapsible: true,\n                                                        children: behavioural_skills.map((skill, index)=>{\n                                                            if (skill.group === \"Execute\") {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                    value: \"item-\".concat(skill.id),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                            className: behavioural_skills.length - 1 == index ? \"bg-dccviolet text-white font-bold p-3 hover:bg-dccviolet/85 rounded-b rounded-t-none min-w-78\" : \"bg-dccviolet text-white font-bold p-3 hover:bg-dccviolet/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                            children: skill.skill_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                            lineNumber: 625,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                        setApi: setCarouselApi,\n                                                                                        opts: {\n                                                                                            loop: true\n                                                                                        },\n                                                                                        className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                children: skill.behavioural_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                            className: \" rounded-lg  mt-3\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                        children: sub_skill.sub_skill_name\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 647,\n                                                                                                                        columnNumber: 45\n                                                                                                                    }, this)\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                    lineNumber: 646,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                        className: \"p-0\",\n                                                                                                                        children: sub_skill[userBLevelDisplay]\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 652,\n                                                                                                                        columnNumber: 45\n                                                                                                                    }, this)\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                    lineNumber: 651,\n                                                                                                                    columnNumber: 43\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                            lineNumber: 645,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    }, sub_skill.id, false, {\n                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                        lineNumber: 644,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                lineNumber: 641,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                lineNumber: 661,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                lineNumber: 662,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                        lineNumber: 636,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                        children: Array.from({\n                                                                                            length: totalItemsExecute\n                                                                                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>scrollToIndex(index),\n                                                                                                className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                            }, index, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                lineNumber: 669,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                        lineNumber: 666,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                            lineNumber: 634,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, skill.id, true, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                    lineNumber: 621,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 605,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row mt-3 pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                        className: \"min-w-8 bg-dccpurple text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                            className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                            children: \"Grow\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                    type: \"single\",\n                                                    collapsible: true,\n                                                    children: behavioural_skills.map((skill, index)=>{\n                                                        if (skill.group === \"Grow\") {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                value: \"item-\".concat(skill.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                        className: behavioural_skills.length - 1 == index ? \"bg-dccpurple text-white font-bold p-3 hover:bg-dccpurple/85 rounded-b rounded-t-none min-w-78\" : \"bg-dccpurple text-white font-bold p-3 hover:bg-dccpurple/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                        children: skill.skill_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                    setApi: setCarouselApi,\n                                                                                    opts: {\n                                                                                        loop: true\n                                                                                    },\n                                                                                    className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                            children: skill.behavioural_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                        className: \" rounded-lg  mt-3\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                    children: sub_skill.sub_skill_name\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                    lineNumber: 733,\n                                                                                                                    columnNumber: 45\n                                                                                                                }, this)\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                lineNumber: 732,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                    className: \"p-0\",\n                                                                                                                    children: sub_skill[userBLevelDisplay]\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                    lineNumber: 738,\n                                                                                                                    columnNumber: 45\n                                                                                                                }, this)\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                lineNumber: 737,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                        lineNumber: 731,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                }, sub_skill.id, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 730,\n                                                                                                    columnNumber: 39\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 727,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 747,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 748,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                    lineNumber: 722,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                    children: Array.from({\n                                                                                        length: totalItemsGrow\n                                                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>scrollToIndex(index),\n                                                                                            className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                        }, index, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 755,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                    lineNumber: 752,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                            lineNumber: 721,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, skill.id, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        }\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                value: \"technical\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        class: \"flex flex-col md:flex-row mt-3\",\n                                        children: [\n                                            skillsLevel1 !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                class: \"flex flex-col md:flex-row mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pl-3 pr-3 pt-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-78 bg-dcclightgrey text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Knowledgable\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: userRole && filteredTechSkills.map((skill, index)=>{\n                                                                const roleMapping = skill.role_mapping.find((mapping)=>mapping.role_id === userRole.id);\n                                                                if (roleMapping.skill_level === 1) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: \"bg-dcclightgrey text-white font-bold p-3 hover:bg-dcclightgrey/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: [\n                                                                                    skill.skill_name,\n                                                                                    getUserTLevel(roleMapping.skill_level)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 810,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.technical_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 831,\n                                                                                                                            columnNumber: 51\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 830,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[getUserTLevel(roleMapping.skill_level)]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 836,\n                                                                                                                            columnNumber: 51\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 835,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                lineNumber: 829,\n                                                                                                                columnNumber: 47\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                            lineNumber: 828,\n                                                                                                            columnNumber: 45\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 825,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 851,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 852,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 820,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItems\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 859,\n                                                                                                    columnNumber: 43\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 856,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                    lineNumber: 819,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 818,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 806,\n                                                                        columnNumber: 31\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 783,\n                                                columnNumber: 17\n                                            }, this),\n                                            skillsLevel2 !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                class: \"flex flex-col md:flex-row mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pl-3 pr-3 pt-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-78 bg-dccorange text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Supported Practitioner\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                    lineNumber: 888,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 887,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: userRole && filteredTechSkills.map((skill, index)=>{\n                                                                const roleMapping = skill.role_mapping.find((mapping)=>mapping.role_id === userRole.id);\n                                                                if (roleMapping.skill_level === 2) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: \"bg-dccorange text-white font-bold p-3 hover:bg-dccorange/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: [\n                                                                                    skill.skill_name,\n                                                                                    getUserTLevel(roleMapping.skill_level)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 910,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.technical_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 931,\n                                                                                                                            columnNumber: 51\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 930,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[getUserTLevel(roleMapping.skill_level)]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 936,\n                                                                                                                            columnNumber: 51\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 935,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                lineNumber: 929,\n                                                                                                                columnNumber: 47\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                            lineNumber: 928,\n                                                                                                            columnNumber: 45\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 925,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 951,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 952,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 920,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItems\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 959,\n                                                                                                    columnNumber: 43\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 956,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                    lineNumber: 919,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 918,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 906,\n                                                                        columnNumber: 31\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 883,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                        lineNumber: 781,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        class: \"flex flex-col md:flex-row mt-3\",\n                                        children: [\n                                            skillsLevel3 !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                class: \"flex flex-col md:flex-row mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pl-3 pr-3 pt-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-78 bg-dccgreen text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Independent Practitioner\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: userRole && filteredTechSkills.map((skill, index)=>{\n                                                                const roleMapping = skill.role_mapping.find((mapping)=>mapping.role_id === userRole.id);\n                                                                if (roleMapping.skill_level === 3) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: \"bg-dccgreen text-white font-bold p-3 hover:bg-dccgreen/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: [\n                                                                                    skill.skill_name,\n                                                                                    getUserTLevel(roleMapping.skill_level)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 1012,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.technical_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 1033,\n                                                                                                                            columnNumber: 51\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 1032,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[getUserTLevel(roleMapping.skill_level)]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 1038,\n                                                                                                                            columnNumber: 51\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 1037,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                lineNumber: 1031,\n                                                                                                                columnNumber: 47\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                            lineNumber: 1030,\n                                                                                                            columnNumber: 45\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 1027,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 1053,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 1054,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 1022,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItems\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 1061,\n                                                                                                    columnNumber: 43\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 1058,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                    lineNumber: 1021,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 1020,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 1008,\n                                                                        columnNumber: 31\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 999,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 985,\n                                                columnNumber: 17\n                                            }, this),\n                                            skillsLevel4 !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                class: \"flex flex-col md:flex-row mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pl-3 pr-3 pt-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-78 bg-dcclightblue text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Expert\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                    lineNumber: 1090,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                lineNumber: 1089,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: userRole && filteredTechSkills.map((skill, index)=>{\n                                                                const roleMapping = skill.role_mapping.find((mapping)=>mapping.role_id === userRole.id);\n                                                                if (roleMapping.skill_level === 4) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: \"bg-dcclightblue text-white font-bold p-3 hover:bg-dcclightblue/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: [\n                                                                                    skill.skill_name,\n                                                                                    getUserTLevel(roleMapping.skill_level)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 1112,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.technical_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 1133,\n                                                                                                                            columnNumber: 51\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 1132,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[getUserTLevel(roleMapping.skill_level)]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                            lineNumber: 1138,\n                                                                                                                            columnNumber: 51\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                        lineNumber: 1137,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                                lineNumber: 1131,\n                                                                                                                columnNumber: 47\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                            lineNumber: 1130,\n                                                                                                            columnNumber: 45\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 1127,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 1153,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 1154,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 1122,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItems\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                                    lineNumber: 1161,\n                                                                                                    columnNumber: 43\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                            lineNumber: 1158,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                    lineNumber: 1121,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                                lineNumber: 1120,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                                        lineNumber: 1108,\n                                                                        columnNumber: 31\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                            lineNumber: 1099,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                    lineNumber: 1086,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 1085,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                        lineNumber: 983,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 780,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n        lineNumber: 372,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"iD4s0BSQAtN8XUxkGjH6O7T331w=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/dashboard.js\n"));

/***/ })

});