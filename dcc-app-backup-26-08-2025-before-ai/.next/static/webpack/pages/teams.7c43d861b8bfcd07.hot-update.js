"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/teams",{

/***/ "(pages-dir-browser)/./pages/teams.js":
/*!************************!*\
  !*** ./pages/teams.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(pages-dir-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/separator */ \"(pages-dir-browser)/./core/components/ui/separator.jsx\");\n/* harmony import */ var _core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/ui/card */ \"(pages-dir-browser)/./core/components/ui/card.jsx\");\n/* harmony import */ var _core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/accordion */ \"(pages-dir-browser)/./core/components/ui/accordion.jsx\");\n/* harmony import */ var _core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/carousel */ \"(pages-dir-browser)/./core/components/ui/carousel.jsx\");\n/* harmony import */ var _core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/components/ui/table */ \"(pages-dir-browser)/./core/components/ui/table.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n/* harmony import */ var _core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/tabs */ \"(pages-dir-browser)/./core/components/ui/tabs.jsx\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_badge__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @core/components/ui/badge */ \"(pages-dir-browser)/./core/components/ui/badge.jsx\");\n/* harmony import */ var _public_skills_chart_png__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../../../../../../../../public/skills-chart.png */ \"(pages-dir-browser)/./public/skills-chart.png\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_ui_popover__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @core/components/ui/popover */ \"(pages-dir-browser)/./core/components/ui/popover.jsx\");\n/* harmony import */ var _core_lib_utils__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @core/lib/utils */ \"(pages-dir-browser)/./core/lib/utils.js\");\n/* harmony import */ var _core_components_ui_command__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @core/components/ui/command */ \"(pages-dir-browser)/./core/components/ui/command.jsx\");\n/* harmony import */ var _core_components_ui_label__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @core/components/ui/label */ \"(pages-dir-browser)/./core/components/ui/label.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var _core_Teams_TeamBSkills__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @core/Teams/TeamBSkills */ \"(pages-dir-browser)/./core/Teams/TeamBSkills.js\");\n/* harmony import */ var _core_Teams_TeamBSkillsEx__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @core/Teams/TeamBSkillsEx */ \"(pages-dir-browser)/./core/Teams/TeamBSkillsEx.js\");\n/* harmony import */ var _core_Teams_TeamBSkillsGrw__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @core/Teams/TeamBSkillsGrw */ \"(pages-dir-browser)/./core/Teams/TeamBSkillsGrw.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst functions = [\n    {\n        name: \"Security\",\n        value: \"Security\"\n    }\n];\nconst teams = [\n    {\n        name: \"Cyber Security Operations\",\n        value: \"Cyber Security Operations\"\n    }\n];\nconst types = [\n    {\n        name: \"Behavioural skills\",\n        value: \"Behavioural\"\n    },\n    {\n        name: \"Technical skills\",\n        value: \"Technical\"\n    }\n];\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills, tech_data_user, role_data } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFunction, setSelectedFunction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTeam, setSelectedTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTeaData, setSelectedTeamData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userBLevel, setUserBLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userBLevelDisplay, setUserBLevelDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRoleDesc, setUserRoleDesc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filteredTechSkills, setFilteredTechSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBSkills, setShowBSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTSkills, setShowTSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTabView, setShowTabView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [carouselApi, setCarouselApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsThink, setTotalItemsThink] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsExecute, setTotalItemsExecute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsGrow, setTotalItemsGrow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [skillsLevel1, setSkillsLevel1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel2, setSkillsLevel2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel3, setSkillsLevel3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel4, setSkillsLevel4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!carouselApi) return;\n            const updateCarouselState = {\n                \"Dashboard.useEffect.updateCarouselState\": ()=>{\n                    setCurrentIndex(carouselApi.selectedScrollSnap());\n                    setTotalItems(carouselApi.scrollSnapList().length);\n                }\n            }[\"Dashboard.useEffect.updateCarouselState\"];\n            updateCarouselState();\n            carouselApi.on(\"select\", updateCarouselState);\n            return ({\n                \"Dashboard.useEffect\": ()=>{\n                    carouselApi.off(\"select\", updateCarouselState); // Clean up on unmount\n                }\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        carouselApi\n    ]);\n    const scrollToIndex = (index)=>{\n        carouselApi === null || carouselApi === void 0 ? void 0 : carouselApi.scrollTo(index);\n    };\n    /* --- DEBUG --- */ //   console.log(behavioural_skills);\n    //   console.log(\"user data\");\n    //   console.log(userData);\n    //   console.log(\"user role\");\n    //   console.log(userRole);\n    //   console.log(\"selectedTeam\");\n    //   console.log(selectedTeam);\n    //   console.log(\"selectedType\");\n    //   console.log(selectedType);\n    //   console.log(\"selectedRole\");\n    //   console.log(selectedRole);\n    //   console.log(\"role_data\");\n    //   console.log(role_data);\n    //   console.log(\"selectedFunction\");\n    //   console.log(selectedFunction);\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get team roles\n    async function getTheSelectedTeam(team) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name, behavioural_skill_level, role_description\").eq(\"team\", team).select(\"*\");\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                console.log(\"team selected\");\n                console.log(data);\n                setSelectedTeamData(data);\n                let result = data.map((param)=>{\n                    let { id } = param;\n                    return id;\n                });\n                console.log(\"result\");\n                console.log(result);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    const getTeam = (team)=>{\n        getTheSelectedTeam(team);\n    };\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 320,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                    lineNumber: 319,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 318,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n            lineNumber: 317,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_22__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbLink, {\n                                                    children: [\n                                                        selectedTeam ? selectedTeam : \"Select a team\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row gap-2 p-2 pt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex m-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-1 text-md text-center pb-1 text-primary\",\n                                    children: \"Select a tean to view the Team Skills\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        onValueChange: (funcselected)=>setSelectedFunction(funcselected),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-[250px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: selectedFunction || \"Select Function\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectGroup, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectLabel, {\n                                                            children: \"Function\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        functions.map((func)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: func.name,\n                                                                children: func.name\n                                                            }, func.name, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        disabled: !selectedFunction,\n                                        onValueChange: (teamselected)=>{\n                                            setSelectedTeam(teamselected);\n                                        //   filterTheRoles(teamselected);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-[250px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: selectedTeam || \"Select Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectGroup, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectLabel, {\n                                                            children: \"Team\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        teams.map((team)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: team.name,\n                                                                children: team.name\n                                                            }, team.name, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        disabled: !selectedTeam,\n                                        onValueChange: (typeSelected)=>{\n                                            setSelectedType(typeSelected);\n                                            getTeam(selectedTeam);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-[250px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: selectedType || \"Select type\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectGroup, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectLabel, {\n                                                            children: \"Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        types.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: type.value,\n                                                                children: type.name\n                                                            }, type.value, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this),\n                    selectedType == \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                        defaultValue: \"think\",\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsList, {\n                                className: \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                        value: \"think\",\n                                        className: \"bg-[#ca005d] text-white font-extrabold text-md w-[50px] h-[50px] rounded-tr-xl rounded-tl-xl rounded-br-none  rounded-bl-none\",\n                                        children: \"Think\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                        value: \"execute\",\n                                        className: \"bg-[#861889] text-white font-extrabold text-md w-[150px] h-[50px] rounded-tr-xl rounded-tl-xl rounded-br-none  rounded-bl-none\",\n                                        children: \"Execute\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                        value: \"grow\",\n                                        className: \"bg-[#5C2071] text-white font-extrabold text-md w-[150px] h-[50px] rounded-tr-xl rounded-tl-xl rounded-br-none  rounded-bl-none\",\n                                        children: \"Grow\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                value: \"think\",\n                                children: selectedType == \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-1 flex-col gap-0 pl-1 pr-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-[25%_15%_15%_15%_15%_15%]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8 bg-[#ca005d] text-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-extrabold text-lg text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"m-auto\",\n                                                            children: \"Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 477,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8  bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold text-sm text-center p-0\",\n                                                    children: \"Strategic Thinking\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8  bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold text-sm text-center p-0\",\n                                                    children: \"Purposeful Planning\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8  bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold text-sm text-center p-0\",\n                                                    children: \"Shaping Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 500,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Customer Focus\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 507,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center p-0\",\n                                                    children: \"Agile and Adaptable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 514,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Teams_TeamBSkills__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                filteredBSkills: selectedTeaData && selectedTeaData\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 524,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 475,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 474,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                value: \"execute\",\n                                children: selectedType == \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-1 flex-col gap-0 pl-1 pr-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-[25%_18.7%_18.7%_18.7%_18.7%]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8 bg-[#861889] text-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-extrabold text-lg text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"m-auto\",\n                                                            children: \"Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 539,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#861889] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Engage and Influence\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 553,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#861889] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Deliver Results\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 561,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#861889] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Collaborate Openly\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 569,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#861889] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Trust and Integrity\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 577,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Teams_TeamBSkillsEx__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                filteredBSkills: selectedTeaData && selectedTeaData\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 587,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 537,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 536,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                value: \"grow\",\n                                children: selectedType == \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-1 flex-col gap-0 pl-1 pr-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-[25%_25%_25%_25%]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8 bg-[#5c2071] text-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-extrabold text-lg text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"m-auto\",\n                                                            children: \"Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 602,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#5c2071] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Develop Self\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 616,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#5c2071] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Enable Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 624,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#5c2071] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Develop Others\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 632,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Teams_TeamBSkillsGrw__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                filteredBSkills: selectedTeaData && selectedTeaData\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 642,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 600,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 599,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 597,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this),\n                    selectedType == \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col gap-2 p-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-[10%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-extrabold text-lg text-center\",\n                                        children: \"Skills\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 659,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 658,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold text-xs text-center p-1\",\n                                        children: \"BCDR\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 665,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 664,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold text-xs text-center p-1\",\n                                        children: \"Change Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 673,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 672,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold text-xs text-center p-1\",\n                                        children: \"Certification Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 681,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold  text-xs text-center p-1\",\n                                        children: \"Compliance & Regulatory Assurance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 689,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 688,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold  text-xs text-center p-1\",\n                                        children: \"Data Analytics and Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 697,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 696,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold text-xs text-center p-1\",\n                                        children: \"Development Lifecycle\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 705,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold text-xs text-center p-1\",\n                                        children: \"Incident Response Lifecycle\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 713,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 712,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold text-xs text-center p-1\",\n                                        children: \"Infrastructure and Cloud Computing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 721,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 720,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold  text-xs text-center p-1\",\n                                        children: \"Procurement\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 729,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold text-xs text-center p-1\",\n                                        children: \"Risk Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 737,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 736,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold text-xs text-center p-1\",\n                                        children: \"Supplier Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 745,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 744,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-semibold text-xs text-center p-1\",\n                                        children: \"Threat Intelligence\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 753,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 752,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Head of Cyber Operations\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 764,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 763,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 762,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight] p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 771,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 770,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 777,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 776,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 783,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 782,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 789,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 788,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 795,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 794,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 801,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 800,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 807,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 806,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 813,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 812,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 819,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 818,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 825,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 824,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 831,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 830,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 837,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 836,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"SOC Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 846,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 845,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 844,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 851,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 850,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 857,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 856,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 863,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 862,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 869,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight] p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 875,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 874,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 881,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 880,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 887,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 886,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 893,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 892,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 899,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 898,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 905,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 904,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 911,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 910,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 917,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 916,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Senior Cyber Operations Analyst\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 926,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 925,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 924,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 933,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 932,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 939,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 938,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 945,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 951,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 950,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 957,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 956,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 963,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 962,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 969,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 968,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 975,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 974,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 981,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 980,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 987,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 986,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 993,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 992,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 999,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 998,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Cyber Operations Analyst\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1008,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1007,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1006,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1015,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1014,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1021,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1020,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1027,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1026,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1033,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1032,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1039,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1038,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1045,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1044,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1051,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1050,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1057,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1056,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1063,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1062,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1069,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1068,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1075,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1074,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1081,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1080,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Lead Cyber Security Engineer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1090,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1089,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1088,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1097,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1096,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1103,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1102,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1109,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1115,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1121,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1120,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1127,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1126,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1133,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1132,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1139,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1138,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1145,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1144,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1151,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1150,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1157,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1163,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1162,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Cyber Security Engineer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1172,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1171,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1170,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1179,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1185,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight  p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1191,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1197,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1196,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1203,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1209,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1208,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1215,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1214,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1221,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1227,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1226,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1233,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1232,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1239,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1238,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1245,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Security Business Partner\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1256,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1253,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1252,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1263,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1269,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1268,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1275,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1274,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1281,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1280,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1287,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1286,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1293,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1292,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1299,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1298,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1305,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1304,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1311,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1310,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1317,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1316,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1323,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1322,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1329,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Senior PKI Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1338,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1337,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1345,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1344,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1351,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1350,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1357,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1356,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1363,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1362,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight] text-black\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1369,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1368,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1375,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1374,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1381,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1387,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1386,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1393,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1392,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1399,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1398,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1404,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1411,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1410,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"SMKI RA Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1420,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1419,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1418,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1427,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1426,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1433,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1432,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1439,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1438,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1445,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1444,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1451,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1450,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1457,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1456,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1463,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1462,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1469,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1468,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1475,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1474,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1481,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1480,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1487,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1486,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1493,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1492,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                            lineNumber: 655,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 652,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n        lineNumber: 333,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"G6UwqHhaOWHFjLBIAhvOXlTNlk4=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/teams.js\n"));

/***/ })

});