"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/teams",{

/***/ "(pages-dir-browser)/./pages/teams.js":
/*!************************!*\
  !*** ./pages/teams.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(pages-dir-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/separator */ \"(pages-dir-browser)/./core/components/ui/separator.jsx\");\n/* harmony import */ var _core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/ui/card */ \"(pages-dir-browser)/./core/components/ui/card.jsx\");\n/* harmony import */ var _core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/accordion */ \"(pages-dir-browser)/./core/components/ui/accordion.jsx\");\n/* harmony import */ var _core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/carousel */ \"(pages-dir-browser)/./core/components/ui/carousel.jsx\");\n/* harmony import */ var _core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/components/ui/table */ \"(pages-dir-browser)/./core/components/ui/table.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n/* harmony import */ var _core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/tabs */ \"(pages-dir-browser)/./core/components/ui/tabs.jsx\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_badge__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @core/components/ui/badge */ \"(pages-dir-browser)/./core/components/ui/badge.jsx\");\n/* harmony import */ var _public_skills_chart_png__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../../../../../../../../public/skills-chart.png */ \"(pages-dir-browser)/./public/skills-chart.png\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_ui_popover__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @core/components/ui/popover */ \"(pages-dir-browser)/./core/components/ui/popover.jsx\");\n/* harmony import */ var _core_lib_utils__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @core/lib/utils */ \"(pages-dir-browser)/./core/lib/utils.js\");\n/* harmony import */ var _core_components_ui_command__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @core/components/ui/command */ \"(pages-dir-browser)/./core/components/ui/command.jsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=Check,ChevronLeft,ChevronRight,ChevronsUpDown,CircleCheck!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_components_ui_label__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @core/components/ui/label */ \"(pages-dir-browser)/./core/components/ui/label.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var _core_Roles_RoleTechSkillSummary__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @core/Roles/RoleTechSkillSummary */ \"(pages-dir-browser)/./core/Roles/RoleTechSkillSummary.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst functions = [\n    {\n        name: \"Security\",\n        value: \"Security\"\n    }\n];\nconst teams = [\n    {\n        name: \"Cyber Security Operations\",\n        value: \"Cyber Security Operations\"\n    }\n];\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills, tech_data_user, role_data } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFunction, setSelectedFunction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTeam, setSelectedTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedRoleName, setSelectedRoleName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedRoleId, setSelectedRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filteredRoles, setFilteredRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userBLevel, setUserBLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userBLevelDisplay, setUserBLevelDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRoleDesc, setUserRoleDesc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filteredTechSkills, setFilteredTechSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBSkills, setShowBSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTSkills, setShowTSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showTabView, setShowTabView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [carouselApi, setCarouselApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsThink, setTotalItemsThink] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsExecute, setTotalItemsExecute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsGrow, setTotalItemsGrow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [skillsLevel1, setSkillsLevel1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel2, setSkillsLevel2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel3, setSkillsLevel3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel4, setSkillsLevel4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!carouselApi) return;\n            const updateCarouselState = {\n                \"Dashboard.useEffect.updateCarouselState\": ()=>{\n                    setCurrentIndex(carouselApi.selectedScrollSnap());\n                    setTotalItems(carouselApi.scrollSnapList().length);\n                }\n            }[\"Dashboard.useEffect.updateCarouselState\"];\n            updateCarouselState();\n            carouselApi.on(\"select\", updateCarouselState);\n            return ({\n                \"Dashboard.useEffect\": ()=>{\n                    carouselApi.off(\"select\", updateCarouselState); // Clean up on unmount\n                }\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        carouselApi\n    ]);\n    const scrollToIndex = (index)=>{\n        carouselApi === null || carouselApi === void 0 ? void 0 : carouselApi.scrollTo(index);\n    };\n    /* --- DEBUG --- */ console.log(\"user data\");\n    console.log(userData);\n    //   console.log(\"user role\");\n    //   console.log(userRole);\n    //   console.log(\"selectedTeam\");\n    //   console.log(selectedTeam);\n    //   console.log(\"selectedRole\");\n    //   console.log(selectedRole);\n    //   console.log(\"role_data\");\n    //   console.log(role_data);\n    //   console.log(\"selectedFunction\");\n    //   console.log(selectedFunction);\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get user's role\n    async function getTheSelectedRole(role_id) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name, behavioural_skill_level, role_description\").eq(\"id\", role_id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserRole(data);\n                getUserBLevel(data.behavioural_skill_level);\n                setUserRoleDesc(data.role_description);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    const getSelectedRole = (selected_id)=>{\n        const filteredTech = technical_skills.filter((param)=>{\n            let { role_mapping } = param;\n            return role_mapping.some((n)=>n.role_id === selected_id);\n        });\n        const skills1 = technical_skills.filter((param)=>{\n            let { role_mapping } = param;\n            return role_mapping.some((n)=>n.role_id === selected_id && n.skill_level === 1);\n        });\n        setSkillsLevel1(skills1.length);\n        const skills2 = technical_skills.filter((param)=>{\n            let { role_mapping } = param;\n            return role_mapping.some((n)=>n.role_id === selected_id && n.skill_level === 2);\n        });\n        setSkillsLevel2(skills2.length);\n        const skills3 = technical_skills.filter((param)=>{\n            let { role_mapping } = param;\n            return role_mapping.some((n)=>n.role_id === selected_id && n.skill_level === 3);\n        });\n        setSkillsLevel3(skills3.length);\n        const skills4 = technical_skills.filter((param)=>{\n            let { role_mapping } = param;\n            return role_mapping.some((n)=>n.role_id === selected_id && n.skill_level === 4);\n        });\n        setSkillsLevel4(skills4.length);\n        setFilteredTechSkills(filteredTech);\n        getTheSelectedRole(selected_id);\n    };\n    // set level for b skills\n    const getUserBLevel = (level)=>{\n        switch(level){\n            case 1:\n                setUserBLevel(\"Operational Contributor\");\n                setUserBLevelDisplay(\"level_1_description\");\n                break;\n            case 2:\n                setUserBLevel(\"Advanced Contributor\");\n                setUserBLevelDisplay(\"level_2_description\");\n                break;\n            case 3:\n                setUserBLevel(\"Team Leader\");\n                setUserBLevelDisplay(\"level_3_description\");\n                break;\n            case 4:\n                setUserBLevel(\"Leader of Leaders\");\n                setUserBLevelDisplay(\"level_4_description\");\n                break;\n            case 5:\n                setUserBLevel(\"Organisational Leader\");\n                setUserBLevelDisplay(\"level_5_description\");\n                break;\n        }\n    };\n    // set level for b skills\n    const getUserTLevel = (level)=>{\n        switch(level){\n            case 1:\n                // setUserBLevel(\"Knowledgablee\");\n                return \"level_1_description\";\n                break;\n            case 2:\n                // setUserBLevel(\"Supported Practitioner\");\n                return \"level_2_description\";\n                break;\n            case 3:\n                // setUserBLevel(\"Independent Practitioner\");\n                return \"level_3_description\";\n                break;\n            case 4:\n                // setUserBLevel(\"Expert\");\n                return \"level_4_description\";\n                break;\n        }\n    };\n    // set level for b skills\n    const filterTheRoles = (team_id)=>{\n        const filteredRoles = role_data.filter((el)=>el.team === team_id);\n        setFilteredRoles(filteredRoles);\n    };\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 387,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                    lineNumber: 386,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 385,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n            lineNumber: 384,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_22__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Roles\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbLink, {\n                                                    children: [\n                                                        selectedRole ? selectedRole.role_name : \"Select a role\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row gap-2 p-2 pt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex m-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-1 text-md text-center pb-1 text-primary\",\n                                    children: \"Select a role to view the snapshot\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        onValueChange: (funcselected)=>setSelectedFunction(funcselected),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-[250px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: selectedFunction || \"Select Function\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectGroup, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectLabel, {\n                                                            children: \"Function\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        functions.map((func)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: func.name,\n                                                                children: func.name\n                                                            }, func.name, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        disabled: !selectedFunction,\n                                        onValueChange: (teamselected)=>{\n                                            setSelectedTeam(teamselected);\n                                            filterTheRoles(teamselected);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-[250px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: selectedTeam || \"Select Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectGroup, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectLabel, {\n                                                            children: \"Team\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        teams.map((team)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: team.name,\n                                                                children: team.name\n                                                            }, team.name, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                selectedTeam && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_18__.Popover, {\n                                        open: open,\n                                        onOpenChange: setOpen,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_18__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                                    variant: \"outline\",\n                                                    role: \"combobox\",\n                                                    \"aria-expanded\": open,\n                                                    className: \"w-[300px] justify-between\",\n                                                    children: [\n                                                        selectedRole ? selectedRole.role_name : \"Select role\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_ChevronsUpDown_CircleCheck_lucide_react__WEBPACK_IMPORTED_MODULE_24__.ChevronsUpDown, {\n                                                            className: \"opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_popover__WEBPACK_IMPORTED_MODULE_18__.PopoverContent, {\n                                                className: \"w-[200px] p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_20__.Command, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_20__.CommandInput, {\n                                                            placeholder: \"Search skills...\",\n                                                            className: \"h-9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_20__.CommandList, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_20__.CommandEmpty, {\n                                                                    children: \"No match found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_20__.CommandGroup, {\n                                                                    children: filteredRoles && filteredRoles.map((role)=>{\n                                                                        if (role.role_name !== \"Team Owner\") {\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_command__WEBPACK_IMPORTED_MODULE_20__.CommandItem, {\n                                                                                value: role.role_name,\n                                                                                onSelect: ()=>{\n                                                                                    //   setRoleSelected(\n                                                                                    //     role === selectedRole ? \"\" : role\n                                                                                    //   );\n                                                                                    setSelectedRole(role);\n                                                                                    setSelectedRoleId(role.id);\n                                                                                    getSelectedRole(role.id);\n                                                                                    //   setSelectedRoleId(getSelectedRecord(role.id));\n                                                                                    setOpen(false);\n                                                                                },\n                                                                                children: role.role_name\n                                                                            }, role.role_name, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 520,\n                                                                                columnNumber: 35\n                                                                            }, this);\n                                                                        }\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 507,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 489,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this),\n                    selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                        defaultValue: \"snapshot\",\n                        className: \"relative mr-auto w-full mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsList, {\n                                className: \"w-full justify-start rounded-none border-b bg-transparent p-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                        value: \"snapshot\",\n                                        className: \"relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-dccpink data-[state=active]:text-foreground data-[state=active]:shadow-none \",\n                                        children: \"Snapshot\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                        value: \"behavioural\",\n                                        className: \"relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-dccpink data-[state=active]:text-foreground data-[state=active]:shadow-none \",\n                                        children: \"Behavioural Skills\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 563,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                        value: \"technical\",\n                                        className: \"relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-dccpink data-[state=active]:text-foreground data-[state=active]:shadow-none \",\n                                        children: \"Technical Skills\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                value: \"snapshot\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 ml-3 col-span-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                        className: \"rounded-lg max-w-2xl pb-1 pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                    className: \"text-lg text-gray-700\",\n                                                                    children: \"Behavioural Skills\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    className: \"pb-0 ml-0\",\n                                                                    src: _public_skills_chart_png__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                                                    // width={150}\n                                                                    height: 378,\n                                                                    alt: \"DCC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                        className: \"rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                className: \"text-lg text-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                    children: userRole === null || userRole === void 0 ? void 0 : userRole.role_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                className: \"pb-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                                                        className: \"h-18\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                                                        className: \"w-[100px]\",\n                                                                                        children: \"Behavioural Level:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                        lineNumber: 607,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_badge__WEBPACK_IMPORTED_MODULE_15__.Badge, {\n                                                                                            className: \"bg-dccblue\",\n                                                                                            children: userBLevel\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 611,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                        lineNumber: 610,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 606,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                            lineNumber: 605,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 604,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-semibold pt-6\",\n                                                                        children: [\n                                                                            \" \",\n                                                                            \"Role description:\",\n                                                                            \" \"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm pt-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"pb-7\",\n                                                                            children: userRoleDesc\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 622,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"pt-3 pl-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                                                            className: \"mr-2 ml-1 bg-[#5c2071] hover:bg-[#5c2071]\",\n                                                                            children: \"Download job description\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                            lineNumber: 626,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, this),\n                                        showTSkills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            className: \"rounded-lg m-3 p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                        className: \"text-lg text-gray-700\",\n                                                        children: \"Technical Skills\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Roles_RoleTechSkillSummary__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        filteredTechSkills: filteredTechSkills,\n                                                        userRole: userRole && userRole.id\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 635,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 578,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 576,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                value: \"behavioural\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pl-3 pr-3 pt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-8 bg-dccpink text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Think\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: behavioural_skills.map((skill, index)=>{\n                                                                if (skill.group === \"Think\") {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: behavioural_skills.length - 1 == index + 1 ? \"bg-dccpink text-white font-bold p-3 hover:bg-dccpink/85 rounded-b rounded-t-none min-w-78\" : \"bg-dccpink text-white font-bold p-3 hover:bg-dccpink/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: skill.skill_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.behavioural_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 699,\n                                                                                                                            columnNumber: 49\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 698,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[userBLevelDisplay]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 704,\n                                                                                                                            columnNumber: 49\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 703,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                lineNumber: 697,\n                                                                                                                columnNumber: 45\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                            lineNumber: 696,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 693,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 713,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 714,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 688,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItemsThink\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 722,\n                                                                                                    columnNumber: 39\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 718,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                    lineNumber: 687,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 29\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 655,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 pt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                            className: \"min-w-8 bg-dccviolet text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                children: \"Execute\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                        type: \"single\",\n                                                        collapsible: true,\n                                                        children: behavioural_skills.map((skill, index)=>{\n                                                            if (skill.group === \"Execute\") {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                    value: \"item-\".concat(skill.id),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                            className: behavioural_skills.length - 1 == index ? \"bg-dccviolet text-white font-bold p-3 hover:bg-dccviolet/85 rounded-b rounded-t-none min-w-78\" : \"bg-dccviolet text-white font-bold p-3 hover:bg-dccviolet/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                            children: skill.skill_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                            lineNumber: 763,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                        setApi: setCarouselApi,\n                                                                                        opts: {\n                                                                                            loop: true\n                                                                                        },\n                                                                                        className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                children: skill.behavioural_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                            className: \" rounded-lg  mt-3\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                        children: sub_skill.sub_skill_name\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 785,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, this)\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                    lineNumber: 784,\n                                                                                                                    columnNumber: 45\n                                                                                                                }, this),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                        className: \"p-0\",\n                                                                                                                        children: sub_skill[userBLevelDisplay]\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 790,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, this)\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                    lineNumber: 789,\n                                                                                                                    columnNumber: 45\n                                                                                                                }, this)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                            lineNumber: 783,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this)\n                                                                                                    }, sub_skill.id, false, {\n                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                        lineNumber: 782,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                lineNumber: 779,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                lineNumber: 799,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                lineNumber: 800,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                        lineNumber: 774,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                        children: Array.from({\n                                                                                            length: totalItemsExecute\n                                                                                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>scrollToIndex(index),\n                                                                                                className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                            }, index, false, {\n                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                lineNumber: 808,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                        lineNumber: 804,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 773,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, skill.id, true, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 27\n                                                                }, this);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 743,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 654,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row mt-3 pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                        className: \"min-w-8 bg-dccpurple text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                            className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                            children: \"Grow\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                    type: \"single\",\n                                                    collapsible: true,\n                                                    children: behavioural_skills.map((skill, index)=>{\n                                                        if (skill.group === \"Grow\") {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                value: \"item-\".concat(skill.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                        className: behavioural_skills.length - 1 == index ? \"bg-dccpurple text-white font-bold p-3 hover:bg-dccpurple/85 rounded-b rounded-t-none min-w-78\" : \"bg-dccpurple text-white font-bold p-3 hover:bg-dccpurple/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                        children: skill.skill_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 849,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                    setApi: setCarouselApi,\n                                                                                    opts: {\n                                                                                        loop: true\n                                                                                    },\n                                                                                    className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                            children: skill.behavioural_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                        className: \" rounded-lg  mt-3\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                    children: sub_skill.sub_skill_name\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                    lineNumber: 871,\n                                                                                                                    columnNumber: 47\n                                                                                                                }, this)\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                lineNumber: 870,\n                                                                                                                columnNumber: 45\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                    className: \"p-0\",\n                                                                                                                    children: sub_skill[userBLevelDisplay]\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                    lineNumber: 876,\n                                                                                                                    columnNumber: 47\n                                                                                                                }, this)\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                lineNumber: 875,\n                                                                                                                columnNumber: 45\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                        lineNumber: 869,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, sub_skill.id, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 868,\n                                                                                                    columnNumber: 41\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 865,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 885,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 886,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                    lineNumber: 860,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                    children: Array.from({\n                                                                                        length: totalItemsGrow\n                                                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>scrollToIndex(index),\n                                                                                            className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                        }, index, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 893,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                    lineNumber: 890,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                            lineNumber: 859,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, skill.id, true, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 845,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        }\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 841,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 829,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 653,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                value: \"technical\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        class: \"flex flex-col md:flex-row mt-3\",\n                                        children: [\n                                            skillsLevel1 !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                class: \"flex flex-col md:flex-row mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pl-3 pr-3 pt-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-78 bg-dcclightgrey text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Knowledgable\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 925,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: userRole && filteredTechSkills.map((skill, index)=>{\n                                                                const roleMapping = skill.role_mapping.find((mapping)=>mapping.role_id === userRole.id);\n                                                                if (roleMapping.skill_level === 1) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: \"bg-dcclightgrey text-white font-bold p-3 hover:bg-dcclightgrey/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: skill.skill_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 948,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.technical_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 969,\n                                                                                                                            columnNumber: 53\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 968,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[getUserTLevel(roleMapping.skill_level)]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 974,\n                                                                                                                            columnNumber: 53\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 973,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                lineNumber: 967,\n                                                                                                                columnNumber: 49\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                            lineNumber: 966,\n                                                                                                            columnNumber: 47\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 963,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 989,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 990,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 958,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItems\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 997,\n                                                                                                    columnNumber: 45\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 994,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                    lineNumber: 957,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 956,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 944,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 921,\n                                                columnNumber: 19\n                                            }, this),\n                                            skillsLevel2 !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                class: \"flex flex-col md:flex-row mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pl-3 pr-3 pt-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-78 bg-dccorange text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Supported Practitioner\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 1028,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 1027,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: userRole && filteredTechSkills.map((skill, index)=>{\n                                                                const roleMapping = skill.role_mapping.find((mapping)=>mapping.role_id === userRole.id);\n                                                                if (roleMapping.skill_level === 2) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: \"bg-dccorange text-white font-bold p-3 hover:bg-dccorange/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: skill.skill_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 1050,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.technical_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 1071,\n                                                                                                                            columnNumber: 53\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 1070,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[getUserTLevel(roleMapping.skill_level)]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 1076,\n                                                                                                                            columnNumber: 53\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 1075,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                lineNumber: 1069,\n                                                                                                                columnNumber: 49\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                            lineNumber: 1068,\n                                                                                                            columnNumber: 47\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1065,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1091,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1092,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 1060,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItems\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1099,\n                                                                                                    columnNumber: 45\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 1096,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                    lineNumber: 1059,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 1058,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 1046,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 1037,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 1023,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 919,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        class: \"flex flex-col md:flex-row mt-3\",\n                                        children: [\n                                            skillsLevel3 !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                class: \"flex flex-col md:flex-row mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pl-3 pr-3 pt-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-78 bg-dccgreen text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Independent Practitioner\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 1132,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 1131,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 1130,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: userRole && filteredTechSkills.map((skill, index)=>{\n                                                                const roleMapping = skill.role_mapping.find((mapping)=>mapping.role_id === userRole.id);\n                                                                if (roleMapping.skill_level === 3) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: \"bg-dccgreen text-white font-bold p-3 hover:bg-dccgreen/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: skill.skill_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 1154,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.technical_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 1175,\n                                                                                                                            columnNumber: 53\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 1174,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[getUserTLevel(roleMapping.skill_level)]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 1180,\n                                                                                                                            columnNumber: 53\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 1179,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                lineNumber: 1173,\n                                                                                                                columnNumber: 49\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                            lineNumber: 1172,\n                                                                                                            columnNumber: 47\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1169,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1195,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1196,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 1164,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItems\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1203,\n                                                                                                    columnNumber: 45\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 1200,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                    lineNumber: 1163,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 1162,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 1150,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 1141,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 1128,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 1127,\n                                                columnNumber: 19\n                                            }, this),\n                                            skillsLevel4 !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                class: \"flex flex-col md:flex-row mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pl-3 pr-3 pt-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                className: \"min-w-78 bg-dcclightblue text-white rounded-tl-xl rounded-tr-xl p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                    className: \"font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12\",\n                                                                    children: \"Expert\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                    lineNumber: 1234,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 1233,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 1232,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.Accordion, {\n                                                            type: \"single\",\n                                                            collapsible: true,\n                                                            children: userRole && filteredTechSkills.map((skill, index)=>{\n                                                                const roleMapping = skill.role_mapping.find((mapping)=>mapping.role_id === userRole.id);\n                                                                if (roleMapping.skill_level === 4) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionItem, {\n                                                                        value: \"item-\".concat(skill.id),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionTrigger, {\n                                                                                className: \"bg-dcclightblue text-white font-bold p-3 hover:bg-dcclightblue/85 rounded-b-none rounded-t-none min-w-78\",\n                                                                                children: skill.skill_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 1256,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__.AccordionContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.Carousel, {\n                                                                                            setApi: setCarouselApi,\n                                                                                            opts: {\n                                                                                                loop: true\n                                                                                            },\n                                                                                            className: \"mt-6 mb-3  ml-16 mr-12 max-w-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselContent, {\n                                                                                                    children: skill.technical_sub_skills.map((sub_skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselItem, {\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                                                                                className: \" rounded-lg  mt-3\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                                                                                            children: sub_skill.sub_skill_name\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 1277,\n                                                                                                                            columnNumber: 53\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 1276,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, this),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                            className: \"p-0\",\n                                                                                                                            children: sub_skill[getUserTLevel(roleMapping.skill_level)]\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                            lineNumber: 1282,\n                                                                                                                            columnNumber: 53\n                                                                                                                        }, this)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                        lineNumber: 1281,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, this)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                                lineNumber: 1275,\n                                                                                                                columnNumber: 49\n                                                                                                            }, this)\n                                                                                                        }, sub_skill.id, false, {\n                                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                            lineNumber: 1274,\n                                                                                                            columnNumber: 47\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1271,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselPrevious, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1297,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__.CarouselNext, {}, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1298,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 1266,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20\",\n                                                                                            children: Array.from({\n                                                                                                length: totalItems\n                                                                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>scrollToIndex(index),\n                                                                                                    className: \"w-3 h-3 rounded-full \".concat(currentIndex === index ? \"bg-black\" : \"bg-gray-300\")\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                                    lineNumber: 1305,\n                                                                                                    columnNumber: 45\n                                                                                                }, this))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                            lineNumber: 1302,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                    lineNumber: 1265,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                                lineNumber: 1264,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, skill.id, true, {\n                                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                        lineNumber: 1252,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                }\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 1243,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 1230,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 1229,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 552,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n        lineNumber: 400,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"TcBDkc5Ccnd+xocS4g10kqaDVYg=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL3RlYW1zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNEM7QUFDSjtBQUNUO0FBQ2tCO0FBQ1I7QUFLSjtBQUNxQjtBQVF4QjtBQU1LO0FBUUQ7QUFTSDtBQVNDO0FBTUY7QUFFa0I7QUFDRjtBQUVDO0FBT1g7QUFNSDtBQUVBO0FBU0E7QUFRZjtBQUU0QjtBQUVRO0FBRVU7QUFFcEUsTUFBTXNFLFlBQVk7SUFBQztRQUFFQyxNQUFNO1FBQVlDLE9BQU87SUFBVztDQUFFO0FBRTNELE1BQU1DLFFBQVE7SUFDWjtRQUFFRixNQUFNO1FBQTZCQyxPQUFPO0lBQTRCO0NBQ3pFOztBQUVjLFNBQVNFLFVBQVUsS0FLakM7UUFMaUMsRUFDaENDLGtCQUFrQixFQUNsQkMsZ0JBQWdCLEVBQ2hCQyxjQUFjLEVBQ2RDLFNBQVMsRUFDVixHQUxpQzs7SUFNaEMsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdoRiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNpRixTQUFTQyxXQUFXLEdBQUdsRiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNtRixVQUFVQyxZQUFZLEdBQUdwRiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNxRixVQUFVQyxZQUFZLEdBQUd0RiwrQ0FBUUEsQ0FBQztJQUV6QyxNQUFNLENBQUN1RixrQkFBa0JDLG9CQUFvQixHQUFHeEYsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDeUYsY0FBY0MsZ0JBQWdCLEdBQUcxRiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMyRixjQUFjQyxnQkFBZ0IsR0FBRzVGLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzZGLGtCQUFrQkMsb0JBQW9CLEdBQUc5RiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUMrRixnQkFBZ0JDLGtCQUFrQixHQUFHaEcsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDaUcsZUFBZUMsaUJBQWlCLEdBQUdsRywrQ0FBUUEsQ0FBQztJQUVuRCxNQUFNLENBQUNtRyxNQUFNQyxRQUFRLEdBQUdwRywrQ0FBUUEsQ0FBQztJQUVqQyxNQUFNLENBQUNxRyxZQUFZQyxjQUFjLEdBQUd0RywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN1RyxtQkFBbUJDLHFCQUFxQixHQUFHeEcsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDeUcsY0FBY0MsZ0JBQWdCLEdBQUcxRywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMyRyxvQkFBb0JDLHNCQUFzQixHQUFHNUcsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDNkcsYUFBYUMsZUFBZSxHQUFHOUcsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDK0csYUFBYUMsZUFBZSxHQUFHaEgsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDaUgsYUFBYUMsZUFBZSxHQUFHbEgsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDbUgsYUFBYUMsZUFBZSxHQUFHcEgsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDcUgsY0FBY0MsZ0JBQWdCLEdBQUd0SCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN1SCxZQUFZQyxjQUFjLEdBQUd4SCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN5SCxpQkFBaUJDLG1CQUFtQixHQUFHMUgsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDMkgsbUJBQW1CQyxxQkFBcUIsR0FBRzVILCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzZILGdCQUFnQkMsa0JBQWtCLEdBQUc5SCwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUMrSCxjQUFjQyxnQkFBZ0IsR0FBR2hJLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2lJLGNBQWNDLGdCQUFnQixHQUFHbEksK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDbUksY0FBY0MsZ0JBQWdCLEdBQUdwSSwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNxSSxjQUFjQyxnQkFBZ0IsR0FBR3RJLCtDQUFRQSxDQUFDO0lBRWpELE1BQU11SSxTQUFTckksc0RBQVNBO0lBRXhCRCxnREFBU0E7K0JBQUM7WUFDUiw2QkFBNkI7WUFDN0JHLDJEQUFRQSxDQUFDb0ksSUFBSSxDQUFDQyxVQUFVLEdBQUdDLElBQUk7dUNBQUM7d0JBQUMsRUFBRUMsTUFBTSxFQUFFMUQsT0FBTyxFQUFFLEVBQUU7b0JBQ3BEQyxXQUFXRDtvQkFDWCxJQUFJLENBQUNBLFNBQVM7d0JBQ1osdUNBQXVDO3dCQUN2Q3NELE9BQU9LLElBQUksQ0FBQztvQkFDZDtnQkFDRjs7WUFFQSxnQ0FBZ0M7WUFDaEMsTUFBTSxFQUNKRCxNQUFNLEVBQUVFLFlBQVksRUFBRSxFQUN2QixHQUFHekksMkRBQVFBLENBQUNvSSxJQUFJLENBQUNNLGlCQUFpQjt1Q0FBQyxDQUFDQyxRQUFROUQ7b0JBQzNDQyxXQUFXRDtvQkFDWCxJQUFJLENBQUNBLFNBQVM7d0JBQ1osNENBQTRDO3dCQUM1Q3NELE9BQU9LLElBQUksQ0FBQztvQkFDZDtnQkFDRjs7WUFFQTt1Q0FBTyxJQUFNQyxhQUFhRyxXQUFXOztRQUN2Qzs4QkFBRztRQUFDVDtLQUFPO0lBRVgsK0NBQStDO0lBQy9DdEksZ0RBQVNBOytCQUFDO1lBQ1IsSUFBSWdGLFNBQVM7Z0JBQ1hnRTtZQUNGO1FBQ0Y7OEJBQUc7UUFBQ2hFO0tBQVE7SUFFWmhGLGdEQUFTQTsrQkFBQztZQUNSLElBQUksQ0FBQ2tILGFBQWE7WUFFbEIsTUFBTStCOzJEQUFzQjtvQkFDMUI1QixnQkFBZ0JILFlBQVlnQyxrQkFBa0I7b0JBQzlDM0IsY0FBY0wsWUFBWWlDLGNBQWMsR0FBR0MsTUFBTTtnQkFDbkQ7O1lBRUFIO1lBRUEvQixZQUFZbUMsRUFBRSxDQUFDLFVBQVVKO1lBRXpCO3VDQUFPO29CQUNML0IsWUFBWW9DLEdBQUcsQ0FBQyxVQUFVTCxzQkFBc0Isc0JBQXNCO2dCQUN4RTs7UUFDRjs4QkFBRztRQUFDL0I7S0FBWTtJQUVoQixNQUFNcUMsZ0JBQWdCLENBQUNDO1FBQ3JCdEMsd0JBQUFBLGtDQUFBQSxZQUFhdUMsUUFBUSxDQUFDRDtJQUN4QjtJQUVBLGlCQUFpQixHQUVqQkUsUUFBUUMsR0FBRyxDQUFDO0lBQ1pELFFBQVFDLEdBQUcsQ0FBQ3pFO0lBRVosOEJBQThCO0lBQzlCLDJCQUEyQjtJQUUzQixpQ0FBaUM7SUFDakMsK0JBQStCO0lBRS9CLGlDQUFpQztJQUNqQywrQkFBK0I7SUFFL0IsOEJBQThCO0lBQzlCLDRCQUE0QjtJQUU1QixxQ0FBcUM7SUFDckMsbUNBQW1DO0lBRW5DLGlCQUFpQixHQUVqQixlQUFlOEQ7UUFDYixJQUFJO1lBQ0ZqRSxXQUFXO1lBRVgsTUFBTSxFQUNKMkQsTUFBTSxFQUFFa0IsSUFBSSxFQUFFLEVBQ2YsR0FBRyxNQUFNekosMkRBQVFBLENBQUNvSSxJQUFJLENBQUNzQixPQUFPO1lBRS9CLElBQUksQ0FBQ0QsTUFBTTtnQkFDVCxNQUFNLElBQUlFLE1BQU07WUFDbEI7WUFFQSxJQUFJLEVBQUVwQixJQUFJLEVBQUVxQixLQUFLLEVBQUVDLE1BQU0sRUFBRSxHQUFHLE1BQU03SiwyREFBUUEsQ0FDekM4SixJQUFJLENBQUMsWUFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxNQUFNUCxLQUFLUSxFQUFFLEVBQ2hCQyxNQUFNO1lBRVQsSUFBSU4sU0FBU0MsV0FBVyxLQUFLO2dCQUMzQixNQUFNRDtZQUNSO1lBRUEsSUFBSXJCLE1BQU07Z0JBQ1J2RCxZQUFZdUQ7WUFDZDtRQUNGLEVBQUUsT0FBT3FCLE9BQU87WUFDZEwsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0ksTUFBTU8sT0FBTztRQUN6RCx3RUFBd0U7UUFDeEUsZ0NBQWdDO1FBQ2xDLFNBQVU7WUFDUnZGLFdBQVc7UUFDYjtJQUNGO0lBRUEsa0JBQWtCO0lBQ2xCLGVBQWV3RixtQkFBbUJDLE9BQU87UUFDdkMsSUFBSTtZQUNGekYsV0FBVztZQUVYLE1BQU0sRUFDSjJELE1BQU0sRUFBRWtCLElBQUksRUFBRSxFQUNmLEdBQUcsTUFBTXpKLDJEQUFRQSxDQUFDb0ksSUFBSSxDQUFDc0IsT0FBTztZQUUvQixJQUFJLENBQUNELE1BQU07Z0JBQ1QsTUFBTSxJQUFJRSxNQUFNO1lBQ2xCO1lBRUEsSUFBSSxFQUFFcEIsSUFBSSxFQUFFcUIsS0FBSyxFQUFFQyxNQUFNLEVBQUUsR0FBRyxNQUFNN0osMkRBQVFBLENBQ3pDOEosSUFBSSxDQUFDLFNBQ0xDLE1BQU0sQ0FDTCxrRUFFREMsRUFBRSxDQUFDLE1BQU1LLFNBQ1RILE1BQU07WUFFVCxJQUFJTixTQUFTQyxXQUFXLEtBQUs7Z0JBQzNCLE1BQU1EO1lBQ1I7WUFFQSxJQUFJckIsTUFBTTtnQkFDUnJELFlBQVlxRDtnQkFDWitCLGNBQWMvQixLQUFLZ0MsdUJBQXVCO2dCQUMxQ2pFLGdCQUFnQmlDLEtBQUtpQyxnQkFBZ0I7WUFDdkM7UUFDRixFQUFFLE9BQU9aLE9BQU87WUFDZEwsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0ksTUFBTU8sT0FBTztRQUN6RCx3RUFBd0U7UUFDeEUsZ0NBQWdDO1FBQ2xDLFNBQVU7WUFDUnZGLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTZGLGtCQUFrQixDQUFDQztRQUN2QixNQUFNQyxlQUFlbkcsaUJBQWlCb0csTUFBTSxDQUFDO2dCQUFDLEVBQUVDLFlBQVksRUFBRTttQkFDNURBLGFBQWFDLElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFVixPQUFPLEtBQUtLOztRQUd6QyxNQUFNTSxVQUFVeEcsaUJBQWlCb0csTUFBTSxDQUFDO2dCQUFDLEVBQUVDLFlBQVksRUFBRTttQkFDdkRBLGFBQWFDLElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFVixPQUFPLEtBQUtLLGVBQWVLLEVBQUVFLFdBQVcsS0FBSzs7UUFFMUVyRCxnQkFBZ0JvRCxRQUFRL0IsTUFBTTtRQUU5QixNQUFNaUMsVUFBVTFHLGlCQUFpQm9HLE1BQU0sQ0FBQztnQkFBQyxFQUFFQyxZQUFZLEVBQUU7bUJBQ3ZEQSxhQUFhQyxJQUFJLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRVYsT0FBTyxLQUFLSyxlQUFlSyxFQUFFRSxXQUFXLEtBQUs7O1FBRTFFbkQsZ0JBQWdCb0QsUUFBUWpDLE1BQU07UUFFOUIsTUFBTWtDLFVBQVUzRyxpQkFBaUJvRyxNQUFNLENBQUM7Z0JBQUMsRUFBRUMsWUFBWSxFQUFFO21CQUN2REEsYUFBYUMsSUFBSSxDQUFDLENBQUNDLElBQU1BLEVBQUVWLE9BQU8sS0FBS0ssZUFBZUssRUFBRUUsV0FBVyxLQUFLOztRQUcxRWpELGdCQUFnQm1ELFFBQVFsQyxNQUFNO1FBRTlCLE1BQU1tQyxVQUFVNUcsaUJBQWlCb0csTUFBTSxDQUFDO2dCQUFDLEVBQUVDLFlBQVksRUFBRTttQkFDdkRBLGFBQWFDLElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFVixPQUFPLEtBQUtLLGVBQWVLLEVBQUVFLFdBQVcsS0FBSzs7UUFHMUUvQyxnQkFBZ0JrRCxRQUFRbkMsTUFBTTtRQUU5QnpDLHNCQUFzQm1FO1FBRXRCUCxtQkFBbUJNO0lBQ3JCO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU1KLGdCQUFnQixDQUFDZTtRQUNyQixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0huRixjQUFjO2dCQUNkRSxxQkFBcUI7Z0JBQ3JCO1lBQ0YsS0FBSztnQkFDSEYsY0FBYztnQkFDZEUscUJBQXFCO2dCQUNyQjtZQUNGLEtBQUs7Z0JBQ0hGLGNBQWM7Z0JBQ2RFLHFCQUFxQjtnQkFDckI7WUFDRixLQUFLO2dCQUNIRixjQUFjO2dCQUNkRSxxQkFBcUI7Z0JBQ3JCO1lBQ0YsS0FBSztnQkFDSEYsY0FBYztnQkFDZEUscUJBQXFCO2dCQUNyQjtRQUNKO0lBQ0Y7SUFFQSx5QkFBeUI7SUFDekIsTUFBTWtGLGdCQUFnQixDQUFDRDtRQUNyQixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsa0NBQWtDO2dCQUNsQyxPQUFPO2dCQUNQO1lBQ0YsS0FBSztnQkFDSCwyQ0FBMkM7Z0JBQzNDLE9BQU87Z0JBQ1A7WUFDRixLQUFLO2dCQUNILDZDQUE2QztnQkFDN0MsT0FBTztnQkFDUDtZQUNGLEtBQUs7Z0JBQ0gsMkJBQTJCO2dCQUMzQixPQUFPO2dCQUNQO1FBQ0o7SUFDRjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNRSxpQkFBaUIsQ0FBQ0M7UUFDdEIsTUFBTTNGLGdCQUFnQm5CLFVBQVVrRyxNQUFNLENBQUMsQ0FBQ2EsS0FBT0EsR0FBR0MsSUFBSSxLQUFLRjtRQUMzRDFGLGlCQUFpQkQ7SUFDbkI7SUFFQSxtREFBbUQ7SUFDbkQsSUFBSWxCLFNBQVM7UUFDWCxxQkFDRSw4REFBQ2dIO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQzNMLG1EQUFJQTt3QkFBQzRMLFFBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUt2QjtJQUVBLHVEQUF1RDtJQUN2RCxJQUFJLENBQUNoSCxTQUFTO1FBQ1osT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUMzRSx3RUFBZUE7OzBCQUNkLDhEQUFDOEQscUVBQVVBO2dCQUNUZSxVQUFVQTtnQkFDVlIsb0JBQW9CQTtnQkFDcEJDLGtCQUFrQkE7Ozs7OzswQkFHcEIsOERBQUNwRSxxRUFBWUE7O2tDQUNYLDhEQUFDMEw7d0JBQU9GLFdBQVU7a0NBQ2hCLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUN6TCx1RUFBY0E7Ozs7OzhDQUNmLDhEQUFDRSxvRUFBU0E7b0NBQUMwTCxhQUFZO29DQUFXSCxXQUFVOzs7Ozs7OENBRTVDLDhEQUFDakosdUVBQVVBOzhDQUNULDRFQUFDRywyRUFBY0E7OzBEQUNiLDhEQUFDRiwyRUFBY0E7Z0RBQUNnSixXQUFVOzBEQUN4Qiw0RUFBQy9JLDJFQUFjQTtvREFBQ21KLE1BQUs7OERBQUk7Ozs7Ozs7Ozs7OzBEQUUzQiw4REFBQ2pKLGdGQUFtQkE7Z0RBQUM2SSxXQUFVOzs7Ozs7MERBQy9CLDhEQUFDaEosMkVBQWNBOzBEQUNiLDRFQUFDQywyRUFBY0E7O3dEQUNaMEMsZUFBZUEsYUFBYTBHLFNBQVMsR0FBRzt3REFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBY3RFLDhEQUFDTjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBa0Q7Ozs7Ozs4Q0FHakUsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDL0osK0RBQU1BO3dDQUNMcUssZUFBZSxDQUFDQyxlQUNkL0csb0JBQW9CK0c7OzBEQUd0Qiw4REFBQ25LLHNFQUFhQTtnREFBQzRKLFdBQVU7MERBQ3ZCLDRFQUFDM0osb0VBQVdBO29EQUNWbUssYUFBYWpILG9CQUFvQjs7Ozs7Ozs7Ozs7MERBR3JDLDhEQUFDckQsc0VBQWFBOzBEQUNaLDRFQUFDSyxvRUFBV0E7O3NFQUNWLDhEQUFDRCxvRUFBV0E7c0VBQUM7Ozs7Ozt3REFDWmdDLFVBQVVtSSxHQUFHLENBQUMsQ0FBQ0MscUJBQ2QsOERBQUN2SyxtRUFBVUE7Z0VBQWlCcUMsT0FBT2tJLEtBQUtuSSxJQUFJOzBFQUN6Q21JLEtBQUtuSSxJQUFJOytEQURLbUksS0FBS25JLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FRcEMsOERBQUN3SDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQy9KLCtEQUFNQTt3Q0FDTDBLLFVBQVUsQ0FBQ3BIO3dDQUNYK0csZUFBZSxDQUFDTTs0Q0FDZGxILGdCQUFnQmtIOzRDQUNoQmpCLGVBQWVpQjt3Q0FDakI7OzBEQUVBLDhEQUFDeEssc0VBQWFBO2dEQUFDNEosV0FBVTswREFDdkIsNEVBQUMzSixvRUFBV0E7b0RBQUNtSyxhQUFhL0csZ0JBQWdCOzs7Ozs7Ozs7OzswREFFNUMsOERBQUN2RCxzRUFBYUE7MERBQ1osNEVBQUNLLG9FQUFXQTs7c0VBQ1YsOERBQUNELG9FQUFXQTtzRUFBQzs7Ozs7O3dEQUNabUMsTUFBTWdJLEdBQUcsQ0FBQyxDQUFDWCxxQkFDViw4REFBQzNKLG1FQUFVQTtnRUFBaUJxQyxPQUFPc0gsS0FBS3ZILElBQUk7MEVBQ3pDdUgsS0FBS3ZILElBQUk7K0RBREt1SCxLQUFLdkgsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQVNuQ2tCLDhCQUNDLDhEQUFDc0c7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUM1SSxpRUFBT0E7d0NBQUMrQyxNQUFNQTt3Q0FBTTBHLGNBQWN6Rzs7MERBT2pDLDhEQUFDOUMsd0VBQWNBO2dEQUFDd0osT0FBTzswREFDckIsNEVBQUNsSywrREFBTUE7b0RBQ0xtSyxTQUFRO29EQUNSQyxNQUFLO29EQUNMQyxpQkFBZTlHO29EQUNmNkYsV0FBVTs7d0RBRVRyRyxlQUFlQSxhQUFhMEcsU0FBUyxHQUFHO3NFQUN6Qyw4REFBQ3RJLDBJQUFjQTs0REFBQ2lJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUc5Qiw4REFBQzNJLHdFQUFjQTtnREFBQzJJLFdBQVU7MERBQ3hCLDRFQUFDeEksaUVBQU9BOztzRUFDTiw4REFBQ0csc0VBQVlBOzREQUNYNkksYUFBWTs0REFDWlIsV0FBVTs7Ozs7O3NFQUVaLDhEQUFDbkkscUVBQVdBOzs4RUFDViw4REFBQ0osc0VBQVlBOzhFQUFDOzs7Ozs7OEVBQ2QsOERBQUNDLHNFQUFZQTs4RUFDVnVDLGlCQUNDQSxjQUFjd0csR0FBRyxDQUFDLENBQUNPO3dFQUNqQixJQUFJQSxLQUFLWCxTQUFTLEtBQUssY0FBYzs0RUFDbkMscUJBQ0UsOERBQUN6SSxxRUFBV0E7Z0ZBRVZZLE9BQU93SSxLQUFLWCxTQUFTO2dGQUNyQmEsVUFBVTtvRkFDUixxQkFBcUI7b0ZBQ3JCLHdDQUF3QztvRkFDeEMsT0FBTztvRkFDUHRILGdCQUFnQm9IO29GQUNoQmhILGtCQUFrQmdILEtBQUszQyxFQUFFO29GQUN6QlEsZ0JBQWdCbUMsS0FBSzNDLEVBQUU7b0ZBRXZCLG1EQUFtRDtvRkFFbkRqRSxRQUFRO2dGQUNWOzBGQUVDNEcsS0FBS1gsU0FBUzsrRUFmVlcsS0FBS1gsU0FBUzs7Ozs7d0VBa0J6QjtvRUFDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVduQjFHLDhCQUNDLDhEQUFDbkQsMkRBQUlBO3dCQUNIMkssY0FBYTt3QkFDYm5CLFdBQVU7OzBDQUVWLDhEQUFDdEosK0RBQVFBO2dDQUFDc0osV0FBVTs7a0RBQ2xCLDhEQUFDckosa0VBQVdBO3dDQUNWNkIsT0FBTTt3Q0FDTndILFdBQVU7a0RBQ1g7Ozs7OztrREFHRCw4REFBQ3JKLGtFQUFXQTt3Q0FDVjZCLE9BQU07d0NBQ053SCxXQUFVO2tEQUNYOzs7Ozs7a0RBR0QsOERBQUNySixrRUFBV0E7d0NBQ1Y2QixPQUFNO3dDQUNOd0gsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7OzBDQUlILDhEQUFDdkosa0VBQVdBO2dDQUFDK0IsT0FBTTswQ0FFakIsNEVBQUN1SDs7c0RBQ0MsOERBQUNBOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUN0TCwwREFBSUE7d0RBQUNzTCxXQUFVOzswRUFDZCw4REFBQ2xMLGdFQUFVQTswRUFDVCw0RUFBQ0MsK0RBQVNBO29FQUFDaUwsV0FBVTs4RUFBd0I7Ozs7Ozs7Ozs7OzBFQUkvQyw4REFBQ3JMLGlFQUFXQTswRUFDViw0RUFBQ1IsbURBQUtBO29FQUNKNkwsV0FBVztvRUFDWG9CLEtBQUt0SyxpRUFBV0E7b0VBQ2hCLGNBQWM7b0VBQ2R1SyxRQUFRO29FQUNSQyxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUtaLDhEQUFDdkI7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUN0TCwwREFBSUE7d0RBQUNzTCxXQUFVOzswRUFDZCw4REFBQ2xMLGdFQUFVQTtnRUFBQ2tMLFdBQVU7MEVBQ3BCLDRFQUFDakwsK0RBQVNBOzhFQUFFc0UscUJBQUFBLCtCQUFBQSxTQUFVZ0gsU0FBUzs7Ozs7Ozs7Ozs7MEVBRWpDLDhEQUFDMUwsaUVBQVdBO2dFQUFDcUwsV0FBVTs7a0ZBQ3JCLDhEQUFDdEssNkRBQUtBO3dFQUFDc0ssV0FBVTtrRkFDZiw0RUFBQ2pLLG1FQUFXQTtzRkFDViw0RUFBQ0MsZ0VBQVFBOztrR0FDUCw4REFBQ0YsaUVBQVNBO3dGQUFDa0ssV0FBVTtrR0FBWTs7Ozs7O2tHQUdqQyw4REFBQ2xLLGlFQUFTQTtrR0FDUiw0RUFBQ2UsNkRBQUtBOzRGQUFDbUosV0FBVTtzR0FDZDNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBTVgsOERBQUMwRjt3RUFBSUMsV0FBVTs7NEVBQ1o7NEVBQUk7NEVBQ2E7Ozs7Ozs7a0ZBRXBCLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFDYiw0RUFBQ3VCOzRFQUFFdkIsV0FBVTtzRkFBUXZGOzs7Ozs7Ozs7OztrRkFFdkIsOERBQUNzRjt3RUFBSUMsV0FBVTtrRkFDYiw0RUFBQ3BKLCtEQUFNQTs0RUFBQ29KLFdBQVU7c0ZBQTRDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dDQVF2RWpGLDZCQUNDLDhEQUFDckcsMERBQUlBOzRDQUFDc0wsV0FBVTs7OERBQ2QsOERBQUNsTCxnRUFBVUE7OERBQ1QsNEVBQUNDLCtEQUFTQTt3REFBQ2lMLFdBQVU7a0VBQXdCOzs7Ozs7Ozs7Ozs4REFJL0MsOERBQUNyTCxpRUFBV0E7OERBQ1YsNEVBQUMwRCx5RUFBb0JBO3dEQUNuQnNDLG9CQUFvQkE7d0RBQ3BCdEIsVUFBVUEsWUFBWUEsU0FBU2dGLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBUzdDLDhEQUFDNUgsa0VBQVdBO2dDQUFDK0IsT0FBTTs7a0RBQ2pCLDhEQUFDdUg7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7O3NFQUNDLDhEQUFDQTtzRUFDQyw0RUFBQ3JMLDBEQUFJQTtnRUFBQ3NMLFdBQVU7MEVBQ2QsNEVBQUNyTCxpRUFBV0E7b0VBQ1ZxTCxXQUNFOzhFQUVIOzs7Ozs7Ozs7Ozs7Ozs7O3NFQU1MLDhEQUFDaEwsb0VBQVNBOzREQUFDd00sTUFBSzs0REFBU0MsV0FBVztzRUFDakM5SSxtQkFBbUI4SCxHQUFHLENBQUMsQ0FBQ2lCLE9BQU9qRTtnRUFDOUIsSUFBSWlFLE1BQU1DLEtBQUssS0FBSyxTQUFTO29FQUMzQixxQkFDRSw4REFBQ3pNLHdFQUFhQTt3RUFFWnNELE9BQU8sUUFBaUIsT0FBVGtKLE1BQU1yRCxFQUFFOzswRkFFdkIsOERBQUNsSiwyRUFBZ0JBO2dGQUNmNkssV0FDRXJILG1CQUFtQjBFLE1BQU0sR0FBRyxLQUFLSSxRQUFRLElBQ3JDLDhGQUNBOzBGQUdMaUUsTUFBTUUsVUFBVTs7Ozs7OzBGQUVuQiw4REFBQzNNLDJFQUFnQkE7MEZBQ2YsNEVBQUM4SztvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUM1SyxtRUFBUUE7NEZBQ1B5TSxRQUFRekc7NEZBQ1IwRyxNQUFNO2dHQUFFQyxNQUFNOzRGQUFLOzRGQUNuQi9CLFdBQVU7OzhHQUVWLDhEQUFDM0ssMEVBQWVBOzhHQUNicU0sTUFBTU0sc0JBQXNCLENBQUN2QixHQUFHLENBQy9CLENBQUN3QiwwQkFDQyw4REFBQzNNLHVFQUFZQTtzSEFDWCw0RUFBQ1osMERBQUlBO2dIQUFDc0wsV0FBVTs7a0lBQ2QsOERBQUNsTCxnRUFBVUE7a0lBQ1QsNEVBQUNDLCtEQUFTQTtzSUFDUGtOLFVBQVVDLGNBQWM7Ozs7Ozs7Ozs7O2tJQUc3Qiw4REFBQ3ZOLGlFQUFXQTtrSUFDViw0RUFBQzRNOzRIQUFFdkIsV0FBVTtzSUFDVmlDLFNBQVMsQ0FBQzFILGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7MkdBVGxCMEgsVUFBVTVELEVBQUU7Ozs7Ozs7Ozs7OEdBaUJyQyw4REFBQzdJLDJFQUFnQkE7Ozs7OzhHQUNqQiw4REFBQ0QsdUVBQVlBOzs7Ozs7Ozs7OztzR0FJZiw4REFBQ3dLOzRGQUFJQyxXQUFVO3NHQUNabUMsTUFBTWpFLElBQUksQ0FBQztnR0FDVmIsUUFBUTVCOzRGQUNWLEdBQUdnRixHQUFHLENBQUMsQ0FBQzJCLEdBQUczRSxzQkFDVCw4REFBQzRFO29HQUVDQyxTQUFTLElBQU05RSxjQUFjQztvR0FDN0J1QyxXQUFXLHdCQUlWLE9BSEMzRSxpQkFBaUJvQyxRQUNiLGFBQ0E7bUdBTERBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VFQWpEVmlFLE1BQU1yRCxFQUFFOzs7OztnRUErRG5COzREQUNGOzs7Ozs7Ozs7Ozs7Ozs7OzswREFLTiw4REFBQzBCO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7a0VBQ0MsNEVBQUNyTCwwREFBSUE7NERBQUNzTCxXQUFVO3NFQUNkLDRFQUFDckwsaUVBQVdBO2dFQUNWcUwsV0FDRTswRUFFSDs7Ozs7Ozs7Ozs7Ozs7OztrRUFLTCw4REFBQ2hMLG9FQUFTQTt3REFBQ3dNLE1BQUs7d0RBQVNDLFdBQVc7a0VBQ2pDOUksbUJBQW1COEgsR0FBRyxDQUFDLENBQUNpQixPQUFPakU7NERBQzlCLElBQUlpRSxNQUFNQyxLQUFLLEtBQUssV0FBVztnRUFDN0IscUJBQ0UsOERBQUN6TSx3RUFBYUE7b0VBRVpzRCxPQUFPLFFBQWlCLE9BQVRrSixNQUFNckQsRUFBRTs7c0ZBRXZCLDhEQUFDbEosMkVBQWdCQTs0RUFDZjZLLFdBQ0VySCxtQkFBbUIwRSxNQUFNLEdBQUcsS0FBS0ksUUFDN0Isa0dBQ0E7c0ZBR0xpRSxNQUFNRSxVQUFVOzs7Ozs7c0ZBRW5CLDhEQUFDM00sMkVBQWdCQTtzRkFDZiw0RUFBQzhLO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQzVLLG1FQUFRQTt3RkFDUHlNLFFBQVF6Rzt3RkFDUjBHLE1BQU07NEZBQUVDLE1BQU07d0ZBQUs7d0ZBQ25CL0IsV0FBVTs7MEdBRVYsOERBQUMzSywwRUFBZUE7MEdBQ2JxTSxNQUFNTSxzQkFBc0IsQ0FBQ3ZCLEdBQUcsQ0FDL0IsQ0FBQ3dCLDBCQUNDLDhEQUFDM00sdUVBQVlBO2tIQUNYLDRFQUFDWiwwREFBSUE7NEdBQUNzTCxXQUFVOzs4SEFDZCw4REFBQ2xMLGdFQUFVQTs4SEFDVCw0RUFBQ0MsK0RBQVNBO2tJQUNQa04sVUFBVUMsY0FBYzs7Ozs7Ozs7Ozs7OEhBRzdCLDhEQUFDdk4saUVBQVdBOzhIQUNWLDRFQUFDNE07d0hBQUV2QixXQUFVO2tJQUNWaUMsU0FBUyxDQUFDMUgsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozt1R0FUbEIwSCxVQUFVNUQsRUFBRTs7Ozs7Ozs7OzswR0FpQnJDLDhEQUFDN0ksMkVBQWdCQTs7Ozs7MEdBQ2pCLDhEQUFDRCx1RUFBWUE7Ozs7Ozs7Ozs7O2tHQUlmLDhEQUFDd0s7d0ZBQUlDLFdBQVU7a0dBQ1ptQyxNQUFNakUsSUFBSSxDQUFDOzRGQUNWYixRQUFRMUI7d0ZBQ1YsR0FBRzhFLEdBQUcsQ0FBQyxDQUFDMkIsR0FBRzNFLHNCQUNULDhEQUFDNEU7Z0dBRUNDLFNBQVMsSUFBTTlFLGNBQWNDO2dHQUM3QnVDLFdBQVcsd0JBSVYsT0FIQzNFLGlCQUFpQm9DLFFBQ2IsYUFDQTsrRkFMREE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUVBakRWaUUsTUFBTXJELEVBQUU7Ozs7OzREQStEbkI7d0RBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJTiw4REFBQzBCO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOzhEQUNDLDRFQUFDckwsMERBQUlBO3dEQUFDc0wsV0FBVTtrRUFDZCw0RUFBQ3JMLGlFQUFXQTs0REFDVnFMLFdBQ0U7c0VBRUg7Ozs7Ozs7Ozs7Ozs7Ozs7OERBS0wsOERBQUNoTCxvRUFBU0E7b0RBQUN3TSxNQUFLO29EQUFTQyxXQUFXOzhEQUNqQzlJLG1CQUFtQjhILEdBQUcsQ0FBQyxDQUFDaUIsT0FBT2pFO3dEQUM5QixJQUFJaUUsTUFBTUMsS0FBSyxLQUFLLFFBQVE7NERBQzFCLHFCQUNFLDhEQUFDek0sd0VBQWFBO2dFQUVac0QsT0FBTyxRQUFpQixPQUFUa0osTUFBTXJELEVBQUU7O2tGQUV2Qiw4REFBQ2xKLDJFQUFnQkE7d0VBQ2Y2SyxXQUNFckgsbUJBQW1CMEUsTUFBTSxHQUFHLEtBQUtJLFFBQzdCLGtHQUNBO2tGQUdMaUUsTUFBTUUsVUFBVTs7Ozs7O2tGQUVuQiw4REFBQzNNLDJFQUFnQkE7a0ZBQ2YsNEVBQUM4Szs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUM1SyxtRUFBUUE7b0ZBQ1B5TSxRQUFRekc7b0ZBQ1IwRyxNQUFNO3dGQUFFQyxNQUFNO29GQUFLO29GQUNuQi9CLFdBQVU7O3NHQUVWLDhEQUFDM0ssMEVBQWVBO3NHQUNicU0sTUFBTU0sc0JBQXNCLENBQUN2QixHQUFHLENBQy9CLENBQUN3QiwwQkFDQyw4REFBQzNNLHVFQUFZQTs4R0FDWCw0RUFBQ1osMERBQUlBO3dHQUFDc0wsV0FBVTs7MEhBQ2QsOERBQUNsTCxnRUFBVUE7MEhBQ1QsNEVBQUNDLCtEQUFTQTs4SEFDUGtOLFVBQVVDLGNBQWM7Ozs7Ozs7Ozs7OzBIQUc3Qiw4REFBQ3ZOLGlFQUFXQTswSEFDViw0RUFBQzRNO29IQUFFdkIsV0FBVTs4SEFDVmlDLFNBQVMsQ0FBQzFILGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7bUdBVGxCMEgsVUFBVTVELEVBQUU7Ozs7Ozs7Ozs7c0dBaUJyQyw4REFBQzdJLDJFQUFnQkE7Ozs7O3NHQUNqQiw4REFBQ0QsdUVBQVlBOzs7Ozs7Ozs7Ozs4RkFJZiw4REFBQ3dLO29GQUFJQyxXQUFVOzhGQUNabUMsTUFBTWpFLElBQUksQ0FBQzt3RkFBRWIsUUFBUXhCO29GQUFlLEdBQUc0RSxHQUFHLENBQ3pDLENBQUMyQixHQUFHM0Usc0JBQ0YsOERBQUM0RTs0RkFFQ0MsU0FBUyxJQUFNOUUsY0FBY0M7NEZBQzdCdUMsV0FBVyx3QkFJVixPQUhDM0UsaUJBQWlCb0MsUUFDYixhQUNBOzJGQUxEQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrREFoRFppRSxNQUFNckQsRUFBRTs7Ozs7d0RBK0RuQjtvREFDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBUVIsOERBQUM1SCxrRUFBV0E7Z0NBQUMrQixPQUFNOztrREFDakIsOERBQUN1SDt3Q0FBSXdDLE9BQU07OzRDQUNSeEcsaUJBQWlCLG1CQUNoQiw4REFBQ2dFO2dEQUFJd0MsT0FBTTswREFDVCw0RUFBQ3hDO29EQUFJQyxXQUFVOztzRUFFYiw4REFBQ0Q7c0VBQ0MsNEVBQUNyTCwwREFBSUE7Z0VBQUNzTCxXQUFVOzBFQUNkLDRFQUFDckwsaUVBQVdBO29FQUNWcUwsV0FDRTs4RUFFSDs7Ozs7Ozs7Ozs7Ozs7OztzRUFLTCw4REFBQ2hMLG9FQUFTQTs0REFBQ3dNLE1BQUs7NERBQVNDLFdBQVc7c0VBQ2pDcEksWUFDQ3NCLG1CQUFtQjhGLEdBQUcsQ0FBQyxDQUFDaUIsT0FBT2pFO2dFQUM3QixNQUFNK0UsY0FBY2QsTUFBTXpDLFlBQVksQ0FBQ3dELElBQUksQ0FDekMsQ0FBQ0MsVUFBWUEsUUFBUWpFLE9BQU8sS0FBS3BGLFNBQVNnRixFQUFFO2dFQUc5QyxJQUFJbUUsWUFBWW5ELFdBQVcsS0FBSyxHQUFHO29FQUNqQyxxQkFDRSw4REFBQ25LLHdFQUFhQTt3RUFFWnNELE9BQU8sUUFBaUIsT0FBVGtKLE1BQU1yRCxFQUFFOzswRkFFdkIsOERBQUNsSiwyRUFBZ0JBO2dGQUNmNkssV0FDRTswRkFHRDBCLE1BQU1FLFVBQVU7Ozs7OzswRkFHbkIsOERBQUMzTSwyRUFBZ0JBOzBGQUNmLDRFQUFDOEs7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDNUssbUVBQVFBOzRGQUNQeU0sUUFBUXpHOzRGQUNSMEcsTUFBTTtnR0FBRUMsTUFBTTs0RkFBSzs0RkFDbkIvQixXQUFVOzs4R0FFViw4REFBQzNLLDBFQUFlQTs4R0FDYnFNLE1BQU1pQixvQkFBb0IsQ0FBQ2xDLEdBQUcsQ0FDN0IsQ0FBQ3dCLDBCQUNDLDhEQUFDM00sdUVBQVlBO3NIQUNYLDRFQUFDWiwwREFBSUE7Z0hBQUNzTCxXQUFVOztrSUFDZCw4REFBQ2xMLGdFQUFVQTtrSUFDVCw0RUFBQ0MsK0RBQVNBO3NJQUNQa04sVUFBVUMsY0FBYzs7Ozs7Ozs7Ozs7a0lBRzdCLDhEQUFDdk4saUVBQVdBO2tJQUNWLDRFQUFDNE07NEhBQUV2QixXQUFVO3NJQUVUaUMsU0FBUyxDQUNQdkMsY0FDRThDLFlBQVluRCxXQUFXLEVBRTFCOzs7Ozs7Ozs7Ozs7Ozs7OzsyR0FkUTRDLFVBQVU1RCxFQUFFOzs7Ozs7Ozs7OzhHQXVCckMsOERBQUM3SSwyRUFBZ0JBOzs7Ozs4R0FDakIsOERBQUNELHVFQUFZQTs7Ozs7Ozs7Ozs7c0dBSWYsOERBQUN3Szs0RkFBSUMsV0FBVTtzR0FDWm1DLE1BQU1qRSxJQUFJLENBQUM7Z0dBQUViLFFBQVE5Qjs0RkFBVyxHQUFHa0YsR0FBRyxDQUNyQyxDQUFDMkIsR0FBRzNFLHNCQUNGLDhEQUFDNEU7b0dBRUNDLFNBQVMsSUFDUDlFLGNBQWNDO29HQUVoQnVDLFdBQVcsd0JBSVYsT0FIQzNFLGlCQUFpQm9DLFFBQ2IsYUFDQTttR0FQREE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUVBckRaaUUsTUFBTXJELEVBQUU7Ozs7O2dFQXNFbkI7NERBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7OzRDQU1UcEMsaUJBQWlCLG1CQUNoQiw4REFBQzhEO2dEQUFJd0MsT0FBTTswREFDVCw0RUFBQ3hDO29EQUFJQyxXQUFVOztzRUFFYiw4REFBQ0Q7c0VBQ0MsNEVBQUNyTCwwREFBSUE7Z0VBQUNzTCxXQUFVOzBFQUNkLDRFQUFDckwsaUVBQVdBO29FQUNWcUwsV0FDRTs4RUFFSDs7Ozs7Ozs7Ozs7Ozs7OztzRUFLTCw4REFBQ2hMLG9FQUFTQTs0REFBQ3dNLE1BQUs7NERBQVNDLFdBQVc7c0VBQ2pDcEksWUFDQ3NCLG1CQUFtQjhGLEdBQUcsQ0FBQyxDQUFDaUIsT0FBT2pFO2dFQUM3QixNQUFNK0UsY0FBY2QsTUFBTXpDLFlBQVksQ0FBQ3dELElBQUksQ0FDekMsQ0FBQ0MsVUFBWUEsUUFBUWpFLE9BQU8sS0FBS3BGLFNBQVNnRixFQUFFO2dFQUc5QyxJQUFJbUUsWUFBWW5ELFdBQVcsS0FBSyxHQUFHO29FQUNqQyxxQkFDRSw4REFBQ25LLHdFQUFhQTt3RUFFWnNELE9BQU8sUUFBaUIsT0FBVGtKLE1BQU1yRCxFQUFFOzswRkFFdkIsOERBQUNsSiwyRUFBZ0JBO2dGQUNmNkssV0FDRTswRkFHRDBCLE1BQU1FLFVBQVU7Ozs7OzswRkFHbkIsOERBQUMzTSwyRUFBZ0JBOzBGQUNmLDRFQUFDOEs7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDNUssbUVBQVFBOzRGQUNQeU0sUUFBUXpHOzRGQUNSMEcsTUFBTTtnR0FBRUMsTUFBTTs0RkFBSzs0RkFDbkIvQixXQUFVOzs4R0FFViw4REFBQzNLLDBFQUFlQTs4R0FDYnFNLE1BQU1pQixvQkFBb0IsQ0FBQ2xDLEdBQUcsQ0FDN0IsQ0FBQ3dCLDBCQUNDLDhEQUFDM00sdUVBQVlBO3NIQUNYLDRFQUFDWiwwREFBSUE7Z0hBQUNzTCxXQUFVOztrSUFDZCw4REFBQ2xMLGdFQUFVQTtrSUFDVCw0RUFBQ0MsK0RBQVNBO3NJQUNQa04sVUFBVUMsY0FBYzs7Ozs7Ozs7Ozs7a0lBRzdCLDhEQUFDdk4saUVBQVdBO2tJQUNWLDRFQUFDNE07NEhBQUV2QixXQUFVO3NJQUVUaUMsU0FBUyxDQUNQdkMsY0FDRThDLFlBQVluRCxXQUFXLEVBRTFCOzs7Ozs7Ozs7Ozs7Ozs7OzsyR0FkUTRDLFVBQVU1RCxFQUFFOzs7Ozs7Ozs7OzhHQXVCckMsOERBQUM3SSwyRUFBZ0JBOzs7Ozs4R0FDakIsOERBQUNELHVFQUFZQTs7Ozs7Ozs7Ozs7c0dBSWYsOERBQUN3Szs0RkFBSUMsV0FBVTtzR0FDWm1DLE1BQU1qRSxJQUFJLENBQUM7Z0dBQUViLFFBQVE5Qjs0RkFBVyxHQUFHa0YsR0FBRyxDQUNyQyxDQUFDMkIsR0FBRzNFLHNCQUNGLDhEQUFDNEU7b0dBRUNDLFNBQVMsSUFDUDlFLGNBQWNDO29HQUVoQnVDLFdBQVcsd0JBSVYsT0FIQzNFLGlCQUFpQm9DLFFBQ2IsYUFDQTttR0FQREE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUVBckRaaUUsTUFBTXJELEVBQUU7Ozs7O2dFQXNFbkI7NERBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU9aLDhEQUFDMEI7d0NBQUl3QyxPQUFNOzs0Q0FDUnBHLGlCQUFpQixtQkFDaEIsOERBQUM0RDtnREFBSXdDLE9BQU07MERBQ1QsNEVBQUN4QztvREFBSUMsV0FBVTs7c0VBRWIsOERBQUNEO3NFQUNDLDRFQUFDckwsMERBQUlBO2dFQUFDc0wsV0FBVTswRUFDZCw0RUFBQ3JMLGlFQUFXQTtvRUFDVnFMLFdBQ0U7OEVBRUg7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBS0wsOERBQUNoTCxvRUFBU0E7NERBQUN3TSxNQUFLOzREQUFTQyxXQUFXO3NFQUNqQ3BJLFlBQ0NzQixtQkFBbUI4RixHQUFHLENBQUMsQ0FBQ2lCLE9BQU9qRTtnRUFDN0IsTUFBTStFLGNBQWNkLE1BQU16QyxZQUFZLENBQUN3RCxJQUFJLENBQ3pDLENBQUNDLFVBQVlBLFFBQVFqRSxPQUFPLEtBQUtwRixTQUFTZ0YsRUFBRTtnRUFHOUMsSUFBSW1FLFlBQVluRCxXQUFXLEtBQUssR0FBRztvRUFDakMscUJBQ0UsOERBQUNuSyx3RUFBYUE7d0VBRVpzRCxPQUFPLFFBQWlCLE9BQVRrSixNQUFNckQsRUFBRTs7MEZBRXZCLDhEQUFDbEosMkVBQWdCQTtnRkFDZjZLLFdBQ0U7MEZBR0QwQixNQUFNRSxVQUFVOzs7Ozs7MEZBR25CLDhEQUFDM00sMkVBQWdCQTswRkFDZiw0RUFBQzhLO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQzVLLG1FQUFRQTs0RkFDUHlNLFFBQVF6Rzs0RkFDUjBHLE1BQU07Z0dBQUVDLE1BQU07NEZBQUs7NEZBQ25CL0IsV0FBVTs7OEdBRVYsOERBQUMzSywwRUFBZUE7OEdBQ2JxTSxNQUFNaUIsb0JBQW9CLENBQUNsQyxHQUFHLENBQzdCLENBQUN3QiwwQkFDQyw4REFBQzNNLHVFQUFZQTtzSEFDWCw0RUFBQ1osMERBQUlBO2dIQUFDc0wsV0FBVTs7a0lBQ2QsOERBQUNsTCxnRUFBVUE7a0lBQ1QsNEVBQUNDLCtEQUFTQTtzSUFDUGtOLFVBQVVDLGNBQWM7Ozs7Ozs7Ozs7O2tJQUc3Qiw4REFBQ3ZOLGlFQUFXQTtrSUFDViw0RUFBQzRNOzRIQUFFdkIsV0FBVTtzSUFFVGlDLFNBQVMsQ0FDUHZDLGNBQ0U4QyxZQUFZbkQsV0FBVyxFQUUxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7MkdBZFE0QyxVQUFVNUQsRUFBRTs7Ozs7Ozs7Ozs4R0F1QnJDLDhEQUFDN0ksMkVBQWdCQTs7Ozs7OEdBQ2pCLDhEQUFDRCx1RUFBWUE7Ozs7Ozs7Ozs7O3NHQUlmLDhEQUFDd0s7NEZBQUlDLFdBQVU7c0dBQ1ptQyxNQUFNakUsSUFBSSxDQUFDO2dHQUFFYixRQUFROUI7NEZBQVcsR0FBR2tGLEdBQUcsQ0FDckMsQ0FBQzJCLEdBQUczRSxzQkFDRiw4REFBQzRFO29HQUVDQyxTQUFTLElBQ1A5RSxjQUFjQztvR0FFaEJ1QyxXQUFXLHdCQUlWLE9BSEMzRSxpQkFBaUJvQyxRQUNiLGFBQ0E7bUdBUERBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VFQXJEWmlFLE1BQU1yRCxFQUFFOzs7OztnRUFzRW5COzREQUNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs0Q0FNVGhDLGlCQUFpQixtQkFDaEIsOERBQUMwRDtnREFBSXdDLE9BQU07MERBQ1QsNEVBQUN4QztvREFBSUMsV0FBVTs7c0VBRWIsOERBQUNEO3NFQUNDLDRFQUFDckwsMERBQUlBO2dFQUFDc0wsV0FBVTswRUFDZCw0RUFBQ3JMLGlFQUFXQTtvRUFDVnFMLFdBQ0U7OEVBRUg7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBS0wsOERBQUNoTCxvRUFBU0E7NERBQUN3TSxNQUFLOzREQUFTQyxXQUFXO3NFQUNqQ3BJLFlBQ0NzQixtQkFBbUI4RixHQUFHLENBQUMsQ0FBQ2lCLE9BQU9qRTtnRUFDN0IsTUFBTStFLGNBQWNkLE1BQU16QyxZQUFZLENBQUN3RCxJQUFJLENBQ3pDLENBQUNDLFVBQVlBLFFBQVFqRSxPQUFPLEtBQUtwRixTQUFTZ0YsRUFBRTtnRUFHOUMsSUFBSW1FLFlBQVluRCxXQUFXLEtBQUssR0FBRztvRUFDakMscUJBQ0UsOERBQUNuSyx3RUFBYUE7d0VBRVpzRCxPQUFPLFFBQWlCLE9BQVRrSixNQUFNckQsRUFBRTs7MEZBRXZCLDhEQUFDbEosMkVBQWdCQTtnRkFDZjZLLFdBQ0U7MEZBR0QwQixNQUFNRSxVQUFVOzs7Ozs7MEZBR25CLDhEQUFDM00sMkVBQWdCQTswRkFDZiw0RUFBQzhLO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQzVLLG1FQUFRQTs0RkFDUHlNLFFBQVF6Rzs0RkFDUjBHLE1BQU07Z0dBQUVDLE1BQU07NEZBQUs7NEZBQ25CL0IsV0FBVTs7OEdBRVYsOERBQUMzSywwRUFBZUE7OEdBQ2JxTSxNQUFNaUIsb0JBQW9CLENBQUNsQyxHQUFHLENBQzdCLENBQUN3QiwwQkFDQyw4REFBQzNNLHVFQUFZQTtzSEFDWCw0RUFBQ1osMERBQUlBO2dIQUFDc0wsV0FBVTs7a0lBQ2QsOERBQUNsTCxnRUFBVUE7a0lBQ1QsNEVBQUNDLCtEQUFTQTtzSUFDUGtOLFVBQVVDLGNBQWM7Ozs7Ozs7Ozs7O2tJQUc3Qiw4REFBQ3ZOLGlFQUFXQTtrSUFDViw0RUFBQzRNOzRIQUFFdkIsV0FBVTtzSUFFVGlDLFNBQVMsQ0FDUHZDLGNBQ0U4QyxZQUFZbkQsV0FBVyxFQUUxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7MkdBZFE0QyxVQUFVNUQsRUFBRTs7Ozs7Ozs7Ozs4R0F1QnJDLDhEQUFDN0ksMkVBQWdCQTs7Ozs7OEdBQ2pCLDhEQUFDRCx1RUFBWUE7Ozs7Ozs7Ozs7O3NHQUlmLDhEQUFDd0s7NEZBQUlDLFdBQVU7c0dBQ1ptQyxNQUFNakUsSUFBSSxDQUFDO2dHQUFFYixRQUFROUI7NEZBQVcsR0FBR2tGLEdBQUcsQ0FDckMsQ0FBQzJCLEdBQUczRSxzQkFDRiw4REFBQzRFO29HQUVDQyxTQUFTLElBQ1A5RSxjQUFjQztvR0FFaEJ1QyxXQUFXLHdCQUlWLE9BSEMzRSxpQkFBaUJvQyxRQUNiLGFBQ0E7bUdBUERBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VFQXJEWmlFLE1BQU1yRCxFQUFFOzs7OztnRUFzRW5COzREQUNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWExQjtHQTdzQ3dCM0Y7O1FBc0NQeEUsa0RBQVNBOzs7S0F0Q0Z3RSIsInNvdXJjZXMiOlsiL1VzZXJzL3JpamFzdC9fX2RhdGEvX19kZXYvRXF1YWxpdGFsL2Rldi9kY2MvZGNjLWFwcC9kY2MtYXBwL3BhZ2VzL3RlYW1zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L3JvdXRlclwiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5pbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gXCJAdXRpbHMvc3VwYWJhc2VDbGllbnRcIjtcbmltcG9ydCB7IE92YWwgfSBmcm9tIFwic3ZnLWxvYWRlcnMtcmVhY3RcIjtcbmltcG9ydCB7XG4gIFNpZGViYXJQcm92aWRlcixcbiAgU2lkZWJhclRyaWdnZXIsXG4gIFNpZGViYXJJbnNldCxcbn0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvc2lkZWJhclwiO1xuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvc2VwYXJhdG9yXCI7XG5pbXBvcnQge1xuICBDYXJkLFxuICBDYXJkQ29udGVudCxcbiAgQ2FyZERlc2NyaXB0aW9uLFxuICBDYXJkRm9vdGVyLFxuICBDYXJkSGVhZGVyLFxuICBDYXJkVGl0bGUsXG59IGZyb20gXCJAY29yZS9jb21wb25lbnRzL3VpL2NhcmRcIjtcbmltcG9ydCB7XG4gIEFjY29yZGlvbixcbiAgQWNjb3JkaW9uQ29udGVudCxcbiAgQWNjb3JkaW9uSXRlbSxcbiAgQWNjb3JkaW9uVHJpZ2dlcixcbn0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvYWNjb3JkaW9uXCI7XG5pbXBvcnQge1xuICBDYXJvdXNlbCxcbiAgQ2Fyb3VzZWxDb250ZW50LFxuICBDYXJvdXNlbEl0ZW0sXG4gIENhcm91c2VsTmV4dCxcbiAgQ2Fyb3VzZWxQcmV2aW91cyxcbiAgQ2Fyb3VzZWxBcGksXG59IGZyb20gXCJAY29yZS9jb21wb25lbnRzL3VpL2Nhcm91c2VsXCI7XG5pbXBvcnQge1xuICBUYWJsZSxcbiAgVGFibGVCb2R5LFxuICBUYWJsZUNhcHRpb24sXG4gIFRhYmxlQ2VsbCxcbiAgVGFibGVIZWFkLFxuICBUYWJsZUhlYWRlcixcbiAgVGFibGVSb3csXG59IGZyb20gXCJAY29yZS9jb21wb25lbnRzL3VpL3RhYmxlXCI7XG5pbXBvcnQge1xuICBTZWxlY3QsXG4gIFNlbGVjdENvbnRlbnQsXG4gIFNlbGVjdEl0ZW0sXG4gIFNlbGVjdFRyaWdnZXIsXG4gIFNlbGVjdFZhbHVlLFxuICBTZWxlY3RMYWJlbCxcbiAgU2VsZWN0R3JvdXAsXG59IGZyb20gXCJAY29yZS9jb21wb25lbnRzL3VpL3NlbGVjdFwiO1xuaW1wb3J0IHtcbiAgVGFicyxcbiAgVGFic0NvbnRlbnQsXG4gIFRhYnNMaXN0LFxuICBUYWJzVHJpZ2dlcixcbn0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvdGFic1wiO1xuXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQGNvcmUvY29tcG9uZW50cy91aS9idXR0b25cIjtcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvYmFkZ2VcIjtcblxuaW1wb3J0IHNraWxsc0NoYXJ0IGZyb20gXCIvcHVibGljL3NraWxscy1jaGFydC5wbmdcIjtcbmltcG9ydCB7XG4gIEJyZWFkY3J1bWIsXG4gIEJyZWFkY3J1bWJJdGVtLFxuICBCcmVhZGNydW1iTGluayxcbiAgQnJlYWRjcnVtYkxpc3QsXG4gIEJyZWFkY3J1bWJTZXBhcmF0b3IsXG59IGZyb20gXCJAY29yZS9jb21wb25lbnRzL3VpL2JyZWFkY3J1bWJcIjtcblxuaW1wb3J0IHtcbiAgUG9wb3ZlcixcbiAgUG9wb3ZlckNvbnRlbnQsXG4gIFBvcG92ZXJUcmlnZ2VyLFxufSBmcm9tIFwiQGNvcmUvY29tcG9uZW50cy91aS9wb3BvdmVyXCI7XG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkBjb3JlL2xpYi91dGlsc1wiO1xuXG5pbXBvcnQge1xuICBDb21tYW5kLFxuICBDb21tYW5kRW1wdHksXG4gIENvbW1hbmRHcm91cCxcbiAgQ29tbWFuZElucHV0LFxuICBDb21tYW5kSXRlbSxcbiAgQ29tbWFuZExpc3QsXG59IGZyb20gXCJAY29yZS9jb21wb25lbnRzL3VpL2NvbW1hbmRcIjtcblxuaW1wb3J0IHtcbiAgQ2hlY2ssXG4gIENoZXZyb25zVXBEb3duLFxuICBDaXJjbGVDaGVjayxcbiAgQ2hldnJvblJpZ2h0LFxuICBDaGV2cm9uTGVmdCxcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCJAY29yZS9jb21wb25lbnRzL3VpL2xhYmVsXCI7XG5cbmltcG9ydCB7IEFwcFNpZGViYXIgfSBmcm9tIFwiQGNvcmUvY29tcG9uZW50cy9hcHAtc2lkZWJhclwiO1xuXG5pbXBvcnQgUm9sZVRlY2hTa2lsbFN1bW1hcnkgZnJvbSBcIkBjb3JlL1JvbGVzL1JvbGVUZWNoU2tpbGxTdW1tYXJ5XCI7XG5cbmNvbnN0IGZ1bmN0aW9ucyA9IFt7IG5hbWU6IFwiU2VjdXJpdHlcIiwgdmFsdWU6IFwiU2VjdXJpdHlcIiB9XTtcblxuY29uc3QgdGVhbXMgPSBbXG4gIHsgbmFtZTogXCJDeWJlciBTZWN1cml0eSBPcGVyYXRpb25zXCIsIHZhbHVlOiBcIkN5YmVyIFNlY3VyaXR5IE9wZXJhdGlvbnNcIiB9LFxuXTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkKHtcbiAgYmVoYXZpb3VyYWxfc2tpbGxzLFxuICB0ZWNobmljYWxfc2tpbGxzLFxuICB0ZWNoX2RhdGFfdXNlcixcbiAgcm9sZV9kYXRhLFxufSkge1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3Nlc3Npb24sIHNldFNlc3Npb25dID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFt1c2VyRGF0YSwgc2V0VXNlckRhdGFdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFt1c2VyUm9sZSwgc2V0VXNlclJvbGVdID0gdXNlU3RhdGUobnVsbCk7XG5cbiAgY29uc3QgW3NlbGVjdGVkRnVuY3Rpb24sIHNldFNlbGVjdGVkRnVuY3Rpb25dID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtzZWxlY3RlZFRlYW0sIHNldFNlbGVjdGVkVGVhbV0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkUm9sZSwgc2V0U2VsZWN0ZWRSb2xlXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbc2VsZWN0ZWRSb2xlTmFtZSwgc2V0U2VsZWN0ZWRSb2xlTmFtZV0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkUm9sZUlkLCBzZXRTZWxlY3RlZFJvbGVJZF0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2ZpbHRlcmVkUm9sZXMsIHNldEZpbHRlcmVkUm9sZXNdID0gdXNlU3RhdGUobnVsbCk7XG5cbiAgY29uc3QgW29wZW4sIHNldE9wZW5dID0gdXNlU3RhdGUobnVsbCk7XG5cbiAgY29uc3QgW3VzZXJCTGV2ZWwsIHNldFVzZXJCTGV2ZWxdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFt1c2VyQkxldmVsRGlzcGxheSwgc2V0VXNlckJMZXZlbERpc3BsYXldID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFt1c2VyUm9sZURlc2MsIHNldFVzZXJSb2xlRGVzY10gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2ZpbHRlcmVkVGVjaFNraWxscywgc2V0RmlsdGVyZWRUZWNoU2tpbGxzXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbc2hvd0JTa2lsbHMsIHNldFNob3dCU2tpbGxzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dUU2tpbGxzLCBzZXRTaG93VFNraWxsc10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3Nob3dUYWJWaWV3LCBzZXRTaG93VGFiVmlld10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjYXJvdXNlbEFwaSwgc2V0Q2Fyb3VzZWxBcGldID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtjdXJyZW50SW5kZXgsIHNldEN1cnJlbnRJbmRleF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3RvdGFsSXRlbXMsIHNldFRvdGFsSXRlbXNdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFt0b3RhbEl0ZW1zVGhpbmssIHNldFRvdGFsSXRlbXNUaGlua10gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3RvdGFsSXRlbXNFeGVjdXRlLCBzZXRUb3RhbEl0ZW1zRXhlY3V0ZV0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3RvdGFsSXRlbXNHcm93LCBzZXRUb3RhbEl0ZW1zR3Jvd10gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3NraWxsc0xldmVsMSwgc2V0U2tpbGxzTGV2ZWwxXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbc2tpbGxzTGV2ZWwyLCBzZXRTa2lsbHNMZXZlbDJdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtza2lsbHNMZXZlbDMsIHNldFNraWxsc0xldmVsM10gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3NraWxsc0xldmVsNCwgc2V0U2tpbGxzTGV2ZWw0XSA9IHVzZVN0YXRlKG51bGwpO1xuXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gQ2hlY2sgZm9yIGV4aXN0aW5nIHNlc3Npb25cbiAgICBzdXBhYmFzZS5hdXRoLmdldFNlc3Npb24oKS50aGVuKCh7IGRhdGE6IHsgc2Vzc2lvbiB9IH0pID0+IHtcbiAgICAgIHNldFNlc3Npb24oc2Vzc2lvbik7XG4gICAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgICAgLy8gUmVkaXJlY3QgdG8gaW5kZXggcGFnZSBpZiBubyBzZXNzaW9uXG4gICAgICAgIHJvdXRlci5wdXNoKFwiL1wiKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIExpc3RlbiBmb3IgYXV0aCBzdGF0ZSBjaGFuZ2VzXG4gICAgY29uc3Qge1xuICAgICAgZGF0YTogeyBzdWJzY3JpcHRpb24gfSxcbiAgICB9ID0gc3VwYWJhc2UuYXV0aC5vbkF1dGhTdGF0ZUNoYW5nZSgoX2V2ZW50LCBzZXNzaW9uKSA9PiB7XG4gICAgICBzZXRTZXNzaW9uKHNlc3Npb24pO1xuICAgICAgaWYgKCFzZXNzaW9uKSB7XG4gICAgICAgIC8vIFJlZGlyZWN0IHRvIGluZGV4IHBhZ2UgaWYgc2Vzc2lvbiBpcyBsb3N0XG4gICAgICAgIHJvdXRlci5wdXNoKFwiL1wiKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHJldHVybiAoKSA9PiBzdWJzY3JpcHRpb24udW5zdWJzY3JpYmUoKTtcbiAgfSwgW3JvdXRlcl0pO1xuXG4gIC8vIEZldGNoIHVzZXIgcHJvZmlsZSB3aGVuIHNlc3Npb24gaXMgYXZhaWxhYmxlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNlc3Npb24pIHtcbiAgICAgIGdldFVzZXJQcm9maWxlKCk7XG4gICAgfVxuICB9LCBbc2Vzc2lvbl0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFjYXJvdXNlbEFwaSkgcmV0dXJuO1xuXG4gICAgY29uc3QgdXBkYXRlQ2Fyb3VzZWxTdGF0ZSA9ICgpID0+IHtcbiAgICAgIHNldEN1cnJlbnRJbmRleChjYXJvdXNlbEFwaS5zZWxlY3RlZFNjcm9sbFNuYXAoKSk7XG4gICAgICBzZXRUb3RhbEl0ZW1zKGNhcm91c2VsQXBpLnNjcm9sbFNuYXBMaXN0KCkubGVuZ3RoKTtcbiAgICB9O1xuXG4gICAgdXBkYXRlQ2Fyb3VzZWxTdGF0ZSgpO1xuXG4gICAgY2Fyb3VzZWxBcGkub24oXCJzZWxlY3RcIiwgdXBkYXRlQ2Fyb3VzZWxTdGF0ZSk7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2Fyb3VzZWxBcGkub2ZmKFwic2VsZWN0XCIsIHVwZGF0ZUNhcm91c2VsU3RhdGUpOyAvLyBDbGVhbiB1cCBvbiB1bm1vdW50XG4gICAgfTtcbiAgfSwgW2Nhcm91c2VsQXBpXSk7XG5cbiAgY29uc3Qgc2Nyb2xsVG9JbmRleCA9IChpbmRleCkgPT4ge1xuICAgIGNhcm91c2VsQXBpPy5zY3JvbGxUbyhpbmRleCk7XG4gIH07XG5cbiAgLyogLS0tIERFQlVHIC0tLSAqL1xuXG4gIGNvbnNvbGUubG9nKFwidXNlciBkYXRhXCIpO1xuICBjb25zb2xlLmxvZyh1c2VyRGF0YSk7XG5cbiAgLy8gICBjb25zb2xlLmxvZyhcInVzZXIgcm9sZVwiKTtcbiAgLy8gICBjb25zb2xlLmxvZyh1c2VyUm9sZSk7XG5cbiAgLy8gICBjb25zb2xlLmxvZyhcInNlbGVjdGVkVGVhbVwiKTtcbiAgLy8gICBjb25zb2xlLmxvZyhzZWxlY3RlZFRlYW0pO1xuXG4gIC8vICAgY29uc29sZS5sb2coXCJzZWxlY3RlZFJvbGVcIik7XG4gIC8vICAgY29uc29sZS5sb2coc2VsZWN0ZWRSb2xlKTtcblxuICAvLyAgIGNvbnNvbGUubG9nKFwicm9sZV9kYXRhXCIpO1xuICAvLyAgIGNvbnNvbGUubG9nKHJvbGVfZGF0YSk7XG5cbiAgLy8gICBjb25zb2xlLmxvZyhcInNlbGVjdGVkRnVuY3Rpb25cIik7XG4gIC8vICAgY29uc29sZS5sb2coc2VsZWN0ZWRGdW5jdGlvbik7XG5cbiAgLyogLS0tIERFQlVHIC0tLSAqL1xuXG4gIGFzeW5jIGZ1bmN0aW9uIGdldFVzZXJQcm9maWxlKCkge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuXG4gICAgICBjb25zdCB7XG4gICAgICAgIGRhdGE6IHsgdXNlciB9LFxuICAgICAgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuXG4gICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTm8gdXNlciBmb3VuZFwiKTtcbiAgICAgIH1cblxuICAgICAgbGV0IHsgZGF0YSwgZXJyb3IsIHN0YXR1cyB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oXCJwcm9maWxlc1wiKVxuICAgICAgICAuc2VsZWN0KFwiKlwiKVxuICAgICAgICAuZXEoXCJpZFwiLCB1c2VyLmlkKVxuICAgICAgICAuc2luZ2xlKCk7XG5cbiAgICAgIGlmIChlcnJvciAmJiBzdGF0dXMgIT09IDQwNikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cblxuICAgICAgaWYgKGRhdGEpIHtcbiAgICAgICAgc2V0VXNlckRhdGEoZGF0YSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUubG9nKFwiRXJyb3IgZmV0Y2hpbmcgdXNlciBwcm9maWxlOlwiLCBlcnJvci5tZXNzYWdlKTtcbiAgICAgIC8vIElmIHRoZXJlJ3MgYW4gZXJyb3IgZmV0Y2hpbmcgcHJvZmlsZSwgd2UgY2FuIHN0aWxsIHNob3cgdGhlIGRhc2hib2FyZFxuICAgICAgLy8gYnV0IHVzZXJEYXRhIHdpbGwgcmVtYWluIG51bGxcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9XG5cbiAgLy8gZ2V0IHVzZXIncyByb2xlXG4gIGFzeW5jIGZ1bmN0aW9uIGdldFRoZVNlbGVjdGVkUm9sZShyb2xlX2lkKSB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIGNvbnN0IHtcbiAgICAgICAgZGF0YTogeyB1c2VyIH0sXG4gICAgICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XG5cbiAgICAgIGlmICghdXNlcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJObyB1c2VyIGZvdW5kXCIpO1xuICAgICAgfVxuXG4gICAgICBsZXQgeyBkYXRhLCBlcnJvciwgc3RhdHVzIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShcInJvbGVzXCIpXG4gICAgICAgIC5zZWxlY3QoXG4gICAgICAgICAgXCJpZCwgdGVhbSwgcm9sZV9uYW1lLCBiZWhhdmlvdXJhbF9za2lsbF9sZXZlbCwgcm9sZV9kZXNjcmlwdGlvblwiXG4gICAgICAgIClcbiAgICAgICAgLmVxKFwiaWRcIiwgcm9sZV9pZClcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAoZXJyb3IgJiYgc3RhdHVzICE9PSA0MDYpIHtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG5cbiAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgIHNldFVzZXJSb2xlKGRhdGEpO1xuICAgICAgICBnZXRVc2VyQkxldmVsKGRhdGEuYmVoYXZpb3VyYWxfc2tpbGxfbGV2ZWwpO1xuICAgICAgICBzZXRVc2VyUm9sZURlc2MoZGF0YS5yb2xlX2Rlc2NyaXB0aW9uKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5sb2coXCJFcnJvciBmZXRjaGluZyB1c2VyIHByb2ZpbGU6XCIsIGVycm9yLm1lc3NhZ2UpO1xuICAgICAgLy8gSWYgdGhlcmUncyBhbiBlcnJvciBmZXRjaGluZyBwcm9maWxlLCB3ZSBjYW4gc3RpbGwgc2hvdyB0aGUgZGFzaGJvYXJkXG4gICAgICAvLyBidXQgdXNlckRhdGEgd2lsbCByZW1haW4gbnVsbFxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTZWxlY3RlZFJvbGUgPSAoc2VsZWN0ZWRfaWQpID0+IHtcbiAgICBjb25zdCBmaWx0ZXJlZFRlY2ggPSB0ZWNobmljYWxfc2tpbGxzLmZpbHRlcigoeyByb2xlX21hcHBpbmcgfSkgPT5cbiAgICAgIHJvbGVfbWFwcGluZy5zb21lKChuKSA9PiBuLnJvbGVfaWQgPT09IHNlbGVjdGVkX2lkKVxuICAgICk7XG5cbiAgICBjb25zdCBza2lsbHMxID0gdGVjaG5pY2FsX3NraWxscy5maWx0ZXIoKHsgcm9sZV9tYXBwaW5nIH0pID0+XG4gICAgICByb2xlX21hcHBpbmcuc29tZSgobikgPT4gbi5yb2xlX2lkID09PSBzZWxlY3RlZF9pZCAmJiBuLnNraWxsX2xldmVsID09PSAxKVxuICAgICk7XG4gICAgc2V0U2tpbGxzTGV2ZWwxKHNraWxsczEubGVuZ3RoKTtcblxuICAgIGNvbnN0IHNraWxsczIgPSB0ZWNobmljYWxfc2tpbGxzLmZpbHRlcigoeyByb2xlX21hcHBpbmcgfSkgPT5cbiAgICAgIHJvbGVfbWFwcGluZy5zb21lKChuKSA9PiBuLnJvbGVfaWQgPT09IHNlbGVjdGVkX2lkICYmIG4uc2tpbGxfbGV2ZWwgPT09IDIpXG4gICAgKTtcbiAgICBzZXRTa2lsbHNMZXZlbDIoc2tpbGxzMi5sZW5ndGgpO1xuXG4gICAgY29uc3Qgc2tpbGxzMyA9IHRlY2huaWNhbF9za2lsbHMuZmlsdGVyKCh7IHJvbGVfbWFwcGluZyB9KSA9PlxuICAgICAgcm9sZV9tYXBwaW5nLnNvbWUoKG4pID0+IG4ucm9sZV9pZCA9PT0gc2VsZWN0ZWRfaWQgJiYgbi5za2lsbF9sZXZlbCA9PT0gMylcbiAgICApO1xuXG4gICAgc2V0U2tpbGxzTGV2ZWwzKHNraWxsczMubGVuZ3RoKTtcblxuICAgIGNvbnN0IHNraWxsczQgPSB0ZWNobmljYWxfc2tpbGxzLmZpbHRlcigoeyByb2xlX21hcHBpbmcgfSkgPT5cbiAgICAgIHJvbGVfbWFwcGluZy5zb21lKChuKSA9PiBuLnJvbGVfaWQgPT09IHNlbGVjdGVkX2lkICYmIG4uc2tpbGxfbGV2ZWwgPT09IDQpXG4gICAgKTtcblxuICAgIHNldFNraWxsc0xldmVsNChza2lsbHM0Lmxlbmd0aCk7XG5cbiAgICBzZXRGaWx0ZXJlZFRlY2hTa2lsbHMoZmlsdGVyZWRUZWNoKTtcblxuICAgIGdldFRoZVNlbGVjdGVkUm9sZShzZWxlY3RlZF9pZCk7XG4gIH07XG5cbiAgLy8gc2V0IGxldmVsIGZvciBiIHNraWxsc1xuICBjb25zdCBnZXRVc2VyQkxldmVsID0gKGxldmVsKSA9PiB7XG4gICAgc3dpdGNoIChsZXZlbCkge1xuICAgICAgY2FzZSAxOlxuICAgICAgICBzZXRVc2VyQkxldmVsKFwiT3BlcmF0aW9uYWwgQ29udHJpYnV0b3JcIik7XG4gICAgICAgIHNldFVzZXJCTGV2ZWxEaXNwbGF5KFwibGV2ZWxfMV9kZXNjcmlwdGlvblwiKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDI6XG4gICAgICAgIHNldFVzZXJCTGV2ZWwoXCJBZHZhbmNlZCBDb250cmlidXRvclwiKTtcbiAgICAgICAgc2V0VXNlckJMZXZlbERpc3BsYXkoXCJsZXZlbF8yX2Rlc2NyaXB0aW9uXCIpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgMzpcbiAgICAgICAgc2V0VXNlckJMZXZlbChcIlRlYW0gTGVhZGVyXCIpO1xuICAgICAgICBzZXRVc2VyQkxldmVsRGlzcGxheShcImxldmVsXzNfZGVzY3JpcHRpb25cIik7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSA0OlxuICAgICAgICBzZXRVc2VyQkxldmVsKFwiTGVhZGVyIG9mIExlYWRlcnNcIik7XG4gICAgICAgIHNldFVzZXJCTGV2ZWxEaXNwbGF5KFwibGV2ZWxfNF9kZXNjcmlwdGlvblwiKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDU6XG4gICAgICAgIHNldFVzZXJCTGV2ZWwoXCJPcmdhbmlzYXRpb25hbCBMZWFkZXJcIik7XG4gICAgICAgIHNldFVzZXJCTGV2ZWxEaXNwbGF5KFwibGV2ZWxfNV9kZXNjcmlwdGlvblwiKTtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICB9O1xuXG4gIC8vIHNldCBsZXZlbCBmb3IgYiBza2lsbHNcbiAgY29uc3QgZ2V0VXNlclRMZXZlbCA9IChsZXZlbCkgPT4ge1xuICAgIHN3aXRjaCAobGV2ZWwpIHtcbiAgICAgIGNhc2UgMTpcbiAgICAgICAgLy8gc2V0VXNlckJMZXZlbChcIktub3dsZWRnYWJsZWVcIik7XG4gICAgICAgIHJldHVybiBcImxldmVsXzFfZGVzY3JpcHRpb25cIjtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDI6XG4gICAgICAgIC8vIHNldFVzZXJCTGV2ZWwoXCJTdXBwb3J0ZWQgUHJhY3RpdGlvbmVyXCIpO1xuICAgICAgICByZXR1cm4gXCJsZXZlbF8yX2Rlc2NyaXB0aW9uXCI7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAzOlxuICAgICAgICAvLyBzZXRVc2VyQkxldmVsKFwiSW5kZXBlbmRlbnQgUHJhY3RpdGlvbmVyXCIpO1xuICAgICAgICByZXR1cm4gXCJsZXZlbF8zX2Rlc2NyaXB0aW9uXCI7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSA0OlxuICAgICAgICAvLyBzZXRVc2VyQkxldmVsKFwiRXhwZXJ0XCIpO1xuICAgICAgICByZXR1cm4gXCJsZXZlbF80X2Rlc2NyaXB0aW9uXCI7XG4gICAgICAgIGJyZWFrO1xuICAgIH1cbiAgfTtcblxuICAvLyBzZXQgbGV2ZWwgZm9yIGIgc2tpbGxzXG4gIGNvbnN0IGZpbHRlclRoZVJvbGVzID0gKHRlYW1faWQpID0+IHtcbiAgICBjb25zdCBmaWx0ZXJlZFJvbGVzID0gcm9sZV9kYXRhLmZpbHRlcigoZWwpID0+IGVsLnRlYW0gPT09IHRlYW1faWQpO1xuICAgIHNldEZpbHRlcmVkUm9sZXMoZmlsdGVyZWRSb2xlcyk7XG4gIH07XG5cbiAgLy8gU2hvdyBsb2FkaW5nIHN0YXRlIHdoaWxlIGNoZWNraW5nIGF1dGhlbnRpY2F0aW9uXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIHBsYWNlLWl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTAgbWItMTAgZmxleCBmbGV4LXJvdyBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxPdmFsIHN0cm9rZT1cIiMwYzM5YWNcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICAvLyBEb24ndCByZW5kZXIgZGFzaGJvYXJkIGlmIG5vIHNlc3Npb24gKHdpbGwgcmVkaXJlY3QpXG4gIGlmICghc2Vzc2lvbikge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8U2lkZWJhclByb3ZpZGVyPlxuICAgICAgPEFwcFNpZGViYXJcbiAgICAgICAgdXNlckRhdGE9e3VzZXJEYXRhfVxuICAgICAgICBiZWhhdmlvdXJhbF9za2lsbHM9e2JlaGF2aW91cmFsX3NraWxsc31cbiAgICAgICAgdGVjaG5pY2FsX3NraWxscz17dGVjaG5pY2FsX3NraWxsc31cbiAgICAgICAgLy8gaGFuZGxlU2hvd1NraWxscz17aGFuZGxlU2hvd1NraWxsc31cbiAgICAgIC8+XG4gICAgICA8U2lkZWJhckluc2V0PlxuICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImZsZXggaC0xNiBzaHJpbmstMCBpdGVtcy1jZW50ZXIgZ2FwLTIgYm9yZGVyLWJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTNcIj5cbiAgICAgICAgICAgIDxTaWRlYmFyVHJpZ2dlciAvPlxuICAgICAgICAgICAgPFNlcGFyYXRvciBvcmllbnRhdGlvbj1cInZlcnRpY2FsXCIgY2xhc3NOYW1lPVwibXItMiBoLTRcIiAvPlxuXG4gICAgICAgICAgICA8QnJlYWRjcnVtYj5cbiAgICAgICAgICAgICAgPEJyZWFkY3J1bWJMaXN0PlxuICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iSXRlbSBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2tcIj5cbiAgICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iTGluayBocmVmPVwiI1wiPlJvbGVzPC9CcmVhZGNydW1iTGluaz5cbiAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJJdGVtPlxuICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iU2VwYXJhdG9yIGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiIC8+XG4gICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJJdGVtPlxuICAgICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJMaW5rPlxuICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRSb2xlID8gc2VsZWN0ZWRSb2xlLnJvbGVfbmFtZSA6IFwiU2VsZWN0IGEgcm9sZVwifXtcIiBcIn1cbiAgICAgICAgICAgICAgICAgIDwvQnJlYWRjcnVtYkxpbms+XG4gICAgICAgICAgICAgICAgPC9CcmVhZGNydW1iSXRlbT5cbiAgICAgICAgICAgICAgPC9CcmVhZGNydW1iTGlzdD5cbiAgICAgICAgICAgIDwvQnJlYWRjcnVtYj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9oZWFkZXI+XG5cbiAgICAgICAgey8qIHshc2VsZWN0ZWRSb2xlICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbS1hdXRvIG10LTEyIHRleHQtbGdcIj5cbiAgICAgICAgICAgIFNlbGVjdCBhIHJvbGUgdG8gdmlldyB0aGUgc25hcHNob3RcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX0gKi99XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGdhcC0yIHAtMiBwdC0zXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG0tYXV0b1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC02IHB0LTEgdGV4dC1tZCB0ZXh0LWNlbnRlciBwYi0xIHRleHQtcHJpbWFyeVwiPlxuICAgICAgICAgICAgICBTZWxlY3QgYSByb2xlIHRvIHZpZXcgdGhlIHNuYXBzaG90XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNiBwdC0wXCI+XG4gICAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsoZnVuY3NlbGVjdGVkKSA9PlxuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRGdW5jdGlvbihmdW5jc2VsZWN0ZWQpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwidy1bMjUwcHhdXCI+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWVcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3NlbGVjdGVkRnVuY3Rpb24gfHwgXCJTZWxlY3QgRnVuY3Rpb25cIn1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEdyb3VwPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0TGFiZWw+RnVuY3Rpb248L1NlbGVjdExhYmVsPlxuICAgICAgICAgICAgICAgICAgICB7ZnVuY3Rpb25zLm1hcCgoZnVuYykgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17ZnVuYy5uYW1lfSB2YWx1ZT17ZnVuYy5uYW1lfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmdW5jLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0R3JvdXA+XG4gICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC02IHB0LTBcIj5cbiAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshc2VsZWN0ZWRGdW5jdGlvbn1cbiAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsodGVhbXNlbGVjdGVkKSA9PiB7XG4gICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFRlYW0odGVhbXNlbGVjdGVkKTtcbiAgICAgICAgICAgICAgICAgIGZpbHRlclRoZVJvbGVzKHRlYW1zZWxlY3RlZCk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyIGNsYXNzTmFtZT1cInctWzI1MHB4XVwiPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIHBsYWNlaG9sZGVyPXtzZWxlY3RlZFRlYW0gfHwgXCJTZWxlY3QgVGVhbVwifSAvPlxuICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RHcm91cD5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdExhYmVsPlRlYW08L1NlbGVjdExhYmVsPlxuICAgICAgICAgICAgICAgICAgICB7dGVhbXMubWFwKCh0ZWFtKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0ga2V5PXt0ZWFtLm5hbWV9IHZhbHVlPXt0ZWFtLm5hbWV9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3RlYW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3RHcm91cD5cbiAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHtzZWxlY3RlZFRlYW0gJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTYgcHQtMFwiPlxuICAgICAgICAgICAgICAgIDxQb3BvdmVyIG9wZW49e29wZW59IG9uT3BlbkNoYW5nZT17c2V0T3Blbn0+XG4gICAgICAgICAgICAgICAgICB7LyogPExhYmVsXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtY2VudGVyIHBiLTEgdGV4dC1wcmltYXJ5IGZvbnQtc2VtaWJvbGRcIlxuICAgICAgICAgICAgICAgICAgaHRtbEZvcj1cInNraWxsIHR5cGVcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIFNraWxsXG4gICAgICAgICAgICAgICAgPC9MYWJlbD4gKi99XG4gICAgICAgICAgICAgICAgICA8UG9wb3ZlclRyaWdnZXIgYXNDaGlsZD5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICByb2xlPVwiY29tYm9ib3hcIlxuICAgICAgICAgICAgICAgICAgICAgIGFyaWEtZXhwYW5kZWQ9e29wZW59XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1bMzAwcHhdIGp1c3RpZnktYmV0d2VlblwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRSb2xlID8gc2VsZWN0ZWRSb2xlLnJvbGVfbmFtZSA6IFwiU2VsZWN0IHJvbGVcIn1cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbnNVcERvd24gY2xhc3NOYW1lPVwib3BhY2l0eS01MFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9Qb3BvdmVyVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgIDxQb3BvdmVyQ29udGVudCBjbGFzc05hbWU9XCJ3LVsyMDBweF0gcC0wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxDb21tYW5kPlxuICAgICAgICAgICAgICAgICAgICAgIDxDb21tYW5kSW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIHNraWxscy4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTlcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPENvbW1hbmRMaXN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPENvbW1hbmRFbXB0eT5ObyBtYXRjaCBmb3VuZDwvQ29tbWFuZEVtcHR5PlxuICAgICAgICAgICAgICAgICAgICAgICAgPENvbW1hbmRHcm91cD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkUm9sZXMgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXJlZFJvbGVzLm1hcCgocm9sZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJvbGUucm9sZV9uYW1lICE9PSBcIlRlYW0gT3duZXJcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb21tYW5kSXRlbVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtyb2xlLnJvbGVfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyb2xlLnJvbGVfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uU2VsZWN0PXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vICAgc2V0Um9sZVNlbGVjdGVkKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgcm9sZSA9PT0gc2VsZWN0ZWRSb2xlID8gXCJcIiA6IHJvbGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gICApO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFJvbGUocm9sZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkUm9sZUlkKHJvbGUuaWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZXRTZWxlY3RlZFJvbGUocm9sZS5pZCk7XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gICBzZXRTZWxlY3RlZFJvbGVJZChnZXRTZWxlY3RlZFJlY29yZChyb2xlLmlkKSk7XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0T3BlbihmYWxzZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtyb2xlLnJvbGVfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NvbW1hbmRJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Db21tYW5kR3JvdXA+XG4gICAgICAgICAgICAgICAgICAgICAgPC9Db21tYW5kTGlzdD5cbiAgICAgICAgICAgICAgICAgICAgPC9Db21tYW5kPlxuICAgICAgICAgICAgICAgICAgPC9Qb3BvdmVyQ29udGVudD5cbiAgICAgICAgICAgICAgICA8L1BvcG92ZXI+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAge3NlbGVjdGVkUm9sZSAmJiAoXG4gICAgICAgICAgPFRhYnNcbiAgICAgICAgICAgIGRlZmF1bHRWYWx1ZT1cInNuYXBzaG90XCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIG1yLWF1dG8gdy1mdWxsIG10LTNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxUYWJzTGlzdCBjbGFzc05hbWU9XCJ3LWZ1bGwganVzdGlmeS1zdGFydCByb3VuZGVkLW5vbmUgYm9yZGVyLWIgYmctdHJhbnNwYXJlbnQgcC0wXCI+XG4gICAgICAgICAgICAgIDxUYWJzVHJpZ2dlclxuICAgICAgICAgICAgICAgIHZhbHVlPVwic25hcHNob3RcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHJvdW5kZWQtbm9uZSBib3JkZXItYi0yIGJvcmRlci1iLXRyYW5zcGFyZW50IGJnLXRyYW5zcGFyZW50IHB4LTQgcGItMyBwdC0yIGZvbnQtc2VtaWJvbGQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHNoYWRvdy1ub25lIHRyYW5zaXRpb24tbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMCBkYXRhLVtzdGF0ZT1hY3RpdmVdOmJvcmRlci1iLWRjY3BpbmsgZGF0YS1bc3RhdGU9YWN0aXZlXTp0ZXh0LWZvcmVncm91bmQgZGF0YS1bc3RhdGU9YWN0aXZlXTpzaGFkb3ctbm9uZSBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgU25hcHNob3RcbiAgICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyXG4gICAgICAgICAgICAgICAgdmFsdWU9XCJiZWhhdmlvdXJhbFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgcm91bmRlZC1ub25lIGJvcmRlci1iLTIgYm9yZGVyLWItdHJhbnNwYXJlbnQgYmctdHJhbnNwYXJlbnQgcHgtNCBwYi0zIHB0LTIgZm9udC1zZW1pYm9sZCB0ZXh0LW11dGVkLWZvcmVncm91bmQgc2hhZG93LW5vbmUgdHJhbnNpdGlvbi1ub25lIGZvY3VzLXZpc2libGU6cmluZy0wIGRhdGEtW3N0YXRlPWFjdGl2ZV06Ym9yZGVyLWItZGNjcGluayBkYXRhLVtzdGF0ZT1hY3RpdmVdOnRleHQtZm9yZWdyb3VuZCBkYXRhLVtzdGF0ZT1hY3RpdmVdOnNoYWRvdy1ub25lIFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBCZWhhdmlvdXJhbCBTa2lsbHNcbiAgICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyXG4gICAgICAgICAgICAgICAgdmFsdWU9XCJ0ZWNobmljYWxcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHJvdW5kZWQtbm9uZSBib3JkZXItYi0yIGJvcmRlci1iLXRyYW5zcGFyZW50IGJnLXRyYW5zcGFyZW50IHB4LTQgcGItMyBwdC0yIGZvbnQtc2VtaWJvbGQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHNoYWRvdy1ub25lIHRyYW5zaXRpb24tbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMCBkYXRhLVtzdGF0ZT1hY3RpdmVdOmJvcmRlci1iLWRjY3BpbmsgZGF0YS1bc3RhdGU9YWN0aXZlXTp0ZXh0LWZvcmVncm91bmQgZGF0YS1bc3RhdGU9YWN0aXZlXTpzaGFkb3ctbm9uZSBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgVGVjaG5pY2FsIFNraWxsc1xuICAgICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgPC9UYWJzTGlzdD5cbiAgICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cInNuYXBzaG90XCI+XG4gICAgICAgICAgICAgIHsvKiBTVEFSVCAtIFNuYXBzaG90IGNvbXBvbmVudCAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMSBtbC0zIGNvbC1zcGFuLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwicm91bmRlZC1sZyBtYXgtdy0yeGwgcGItMSBwdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBCZWhhdmlvdXJhbCBTa2lsbHNcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcInBiLTAgbWwtMFwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e3NraWxsc0NoYXJ0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyB3aWR0aD17MTUwfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezM3OH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiRENDXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEgbXItM1wiPlxuICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJyb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPnt1c2VyUm9sZT8ucm9sZV9uYW1lfTwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicGItMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlIGNsYXNzTmFtZT1cImgtMThcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQgY2xhc3NOYW1lPVwidy1bMTAwcHhdXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEJlaGF2aW91cmFsIExldmVsOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY2xhc3NOYW1lPVwiYmctZGNjYmx1ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyQkxldmVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCBwdC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtcIiBcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUm9sZSBkZXNjcmlwdGlvbjp7XCIgXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBwdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInBiLTdcIj57dXNlclJvbGVEZXNjfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC0zIHBsLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBjbGFzc05hbWU9XCJtci0yIG1sLTEgYmctWyM1YzIwNzFdIGhvdmVyOmJnLVsjNWMyMDcxXVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIERvd25sb2FkIGpvYiBkZXNjcmlwdGlvblxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIHtzaG93VFNraWxscyAmJiAoXG4gICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJyb3VuZGVkLWxnIG0tMyBwLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFRlY2huaWNhbCBTa2lsbHNcbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgPFJvbGVUZWNoU2tpbGxTdW1tYXJ5XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXJlZFRlY2hTa2lsbHM9e2ZpbHRlcmVkVGVjaFNraWxsc31cbiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJSb2xlPXt1c2VyUm9sZSAmJiB1c2VyUm9sZS5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICB7LyogRU5EIC0gU25hcHNob3QgY29tcG9uZW50ICovfVxuICAgICAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiYmVoYXZpb3VyYWxcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IG10LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInBsLTMgcHItMyBwdC0wXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1pbi13LTggYmctZGNjcGluayB0ZXh0LXdoaXRlIHJvdW5kZWQtdGwteGwgcm91bmRlZC10ci14bCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudFxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiZm9udC1zZW1pYm9sZCB0ZXh0LTJ4bCB0ZXh0LWNlbnRlciBwLTAgbWluLXctNzggbWwtMTIgbXItMTJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFRoaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvbiB0eXBlPVwic2luZ2xlXCIgY29sbGFwc2libGU+XG4gICAgICAgICAgICAgICAgICAgICAge2JlaGF2aW91cmFsX3NraWxscy5tYXAoKHNraWxsLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHNraWxsLmdyb3VwID09PSBcIlRoaW5rXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uSXRlbVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtza2lsbC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtgaXRlbS0ke3NraWxsLmlkfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvblRyaWdnZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiZWhhdmlvdXJhbF9za2lsbHMubGVuZ3RoIC0gMSA9PSBpbmRleCArIDFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1kY2NwaW5rIHRleHQtd2hpdGUgZm9udC1ib2xkIHAtMyBob3ZlcjpiZy1kY2NwaW5rLzg1IHJvdW5kZWQtYiByb3VuZGVkLXQtbm9uZSBtaW4tdy03OFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYmctZGNjcGluayB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBwLTMgaG92ZXI6YmctZGNjcGluay84NSByb3VuZGVkLWItbm9uZSByb3VuZGVkLXQtbm9uZSBtaW4tdy03OFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NraWxsLnNraWxsX25hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvblRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBweC01IG14LWF1dG8gbXQtNSBtYXgtdy03eGwgbGc6bXQtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0QXBpPXtzZXRDYXJvdXNlbEFwaX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdHM9e3sgbG9vcDogdHJ1ZSB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNiBtYi0zICBtbC0xNiBtci0xMiBtYXgtdy14c1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NraWxsLmJlaGF2aW91cmFsX3N1Yl9za2lsbHMubWFwKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChzdWJfc2tpbGwpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbEl0ZW0ga2V5PXtzdWJfc2tpbGwuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCIgcm91bmRlZC1sZyAgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3N1Yl9za2lsbC5zdWJfc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJwLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3N1Yl9za2lsbFt1c2VyQkxldmVsRGlzcGxheV19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxQcmV2aW91cyAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsTmV4dCAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2Fyb3VzZWw+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogTmF2aWdhdGlvbiBEb3RzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiIGJvdHRvbS0wIGxlZnQtMCByaWdodC0wIGZsZXgganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yIHotMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtBcnJheS5mcm9tKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVuZ3RoOiB0b3RhbEl0ZW1zVGhpbmssXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KS5tYXAoKF8sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNjcm9sbFRvSW5kZXgoaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50SW5kZXggPT09IGluZGV4XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ibGFja1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1ncmF5LTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvbkNvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb25JdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgcHQtMFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwibWluLXctOCBiZy1kY2N2aW9sZXQgdGV4dC13aGl0ZSByb3VuZGVkLXRsLXhsIHJvdW5kZWQtdHIteGwgcC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICBcImZvbnQtc2VtaWJvbGQgdGV4dC0yeGwgdGV4dC1jZW50ZXIgcC0wIG1pbi13LTc4IG1sLTEyIG1yLTEyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBFeGVjdXRlXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uIHR5cGU9XCJzaW5nbGVcIiBjb2xsYXBzaWJsZT5cbiAgICAgICAgICAgICAgICAgICAge2JlaGF2aW91cmFsX3NraWxscy5tYXAoKHNraWxsLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGlmIChza2lsbC5ncm91cCA9PT0gXCJFeGVjdXRlXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxBY2NvcmRpb25JdGVtXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtza2lsbC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17YGl0ZW0tJHtza2lsbC5pZH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvblRyaWdnZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJlaGF2aW91cmFsX3NraWxscy5sZW5ndGggLSAxID09IGluZGV4XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWRjY3Zpb2xldCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBwLTMgaG92ZXI6YmctZGNjdmlvbGV0Lzg1IHJvdW5kZWQtYiByb3VuZGVkLXQtbm9uZSBtaW4tdy03OFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLWRjY3Zpb2xldCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBwLTMgaG92ZXI6YmctZGNjdmlvbGV0Lzg1IHJvdW5kZWQtYi1ub25lIHJvdW5kZWQtdC1ub25lIG1pbi13LTc4XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2tpbGwuc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvblRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvbkNvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHB4LTUgbXgtYXV0byBtdC01IG1heC13LTd4bCBsZzptdC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEFwaT17c2V0Q2Fyb3VzZWxBcGl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0cz17eyBsb29wOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNiBtYi0zICBtbC0xNiBtci0xMiBtYXgtdy14c1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NraWxsLmJlaGF2aW91cmFsX3N1Yl9za2lsbHMubWFwKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoc3ViX3NraWxsKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsSXRlbSBrZXk9e3N1Yl9za2lsbC5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCIgcm91bmRlZC1sZyAgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3N1Yl9za2lsbC5zdWJfc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwicC0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3ViX3NraWxsW3VzZXJCTGV2ZWxEaXNwbGF5XX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsUHJldmlvdXMgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxOZXh0IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2Fyb3VzZWw+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIE5hdmlnYXRpb24gRG90cyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCIgYm90dG9tLTAgbGVmdC0wIHJpZ2h0LTAgZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgei0yMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtBcnJheS5mcm9tKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlbmd0aDogdG90YWxJdGVtc0V4ZWN1dGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkubWFwKChfLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzY3JvbGxUb0luZGV4KGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50SW5kZXggPT09IGluZGV4XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctYmxhY2tcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLWdyYXktMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb25JdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cgbXQtMyBwdC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgcHQtMFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwibWluLXctOCBiZy1kY2NwdXJwbGUgdGV4dC13aGl0ZSByb3VuZGVkLXRsLXhsIHJvdW5kZWQtdHIteGwgcC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICBcImZvbnQtc2VtaWJvbGQgdGV4dC0yeGwgdGV4dC1jZW50ZXIgcC0wIG1pbi13LTc4IG1sLTEyIG1yLTEyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBHcm93XG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uIHR5cGU9XCJzaW5nbGVcIiBjb2xsYXBzaWJsZT5cbiAgICAgICAgICAgICAgICAgICAge2JlaGF2aW91cmFsX3NraWxscy5tYXAoKHNraWxsLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGlmIChza2lsbC5ncm91cCA9PT0gXCJHcm93XCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxBY2NvcmRpb25JdGVtXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtza2lsbC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17YGl0ZW0tJHtza2lsbC5pZH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvblRyaWdnZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJlaGF2aW91cmFsX3NraWxscy5sZW5ndGggLSAxID09IGluZGV4XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWRjY3B1cnBsZSB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBwLTMgaG92ZXI6YmctZGNjcHVycGxlLzg1IHJvdW5kZWQtYiByb3VuZGVkLXQtbm9uZSBtaW4tdy03OFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLWRjY3B1cnBsZSB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBwLTMgaG92ZXI6YmctZGNjcHVycGxlLzg1IHJvdW5kZWQtYi1ub25lIHJvdW5kZWQtdC1ub25lIG1pbi13LTc4XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2tpbGwuc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvblRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvbkNvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHB4LTUgbXgtYXV0byBtdC01IG1heC13LTd4bCBsZzptdC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEFwaT17c2V0Q2Fyb3VzZWxBcGl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0cz17eyBsb29wOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNiBtYi0zICBtbC0xNiBtci0xMiBtYXgtdy14c1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NraWxsLmJlaGF2aW91cmFsX3N1Yl9za2lsbHMubWFwKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoc3ViX3NraWxsKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsSXRlbSBrZXk9e3N1Yl9za2lsbC5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCIgcm91bmRlZC1sZyAgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3N1Yl9za2lsbC5zdWJfc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwicC0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3ViX3NraWxsW3VzZXJCTGV2ZWxEaXNwbGF5XX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsUHJldmlvdXMgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxOZXh0IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2Fyb3VzZWw+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIE5hdmlnYXRpb24gRG90cyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCIgYm90dG9tLTAgbGVmdC0wIHJpZ2h0LTAgZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgei0yMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiB0b3RhbEl0ZW1zR3JvdyB9KS5tYXAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoXywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2Nyb2xsVG9JbmRleChpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRJbmRleCA9PT0gaW5kZXhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWJsYWNrXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLWdyYXktMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb25JdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICAgICAgey8qIHRlY2huaWNhbCBza2lsbHMgKi99XG5cbiAgICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cInRlY2huaWNhbFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBtdC0zXCI+XG4gICAgICAgICAgICAgICAge3NraWxsc0xldmVsMSAhPT0gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBtdC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGwtMyBwci0zIHB0LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogLy8gc2V0IHRoZSBsZXZlbCBhbmQgc3RhcnQgdGhlIG1hcCBoZXJlICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJtaW4tdy03OCBiZy1kY2NsaWdodGdyZXkgdGV4dC13aGl0ZSByb3VuZGVkLXRsLXhsIHJvdW5kZWQtdHIteGwgcC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcImZvbnQtc2VtaWJvbGQgdGV4dC0yeGwgdGV4dC1jZW50ZXIgcC0wIG1pbi13LTc4IG1sLTEyIG1yLTEyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBLbm93bGVkZ2FibGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uIHR5cGU9XCJzaW5nbGVcIiBjb2xsYXBzaWJsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyUm9sZSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXJlZFRlY2hTa2lsbHMubWFwKChza2lsbCwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByb2xlTWFwcGluZyA9IHNraWxsLnJvbGVfbWFwcGluZy5maW5kKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKG1hcHBpbmcpID0+IG1hcHBpbmcucm9sZV9pZCA9PT0gdXNlclJvbGUuaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJvbGVNYXBwaW5nLnNraWxsX2xldmVsID09PSAxKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uSXRlbVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17c2tpbGwuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2BpdGVtLSR7c2tpbGwuaWR9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBY2NvcmRpb25UcmlnZ2VyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcImJnLWRjY2xpZ2h0Z3JleSB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBwLTMgaG92ZXI6YmctZGNjbGlnaHRncmV5Lzg1IHJvdW5kZWQtYi1ub25lIHJvdW5kZWQtdC1ub25lIG1pbi13LTc4XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2tpbGwuc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiB7Z2V0VXNlclRMZXZlbChyb2xlTWFwcGluZy5za2lsbF9sZXZlbCl9ICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgcHgtNSBteC1hdXRvIG10LTUgbWF4LXctN3hsIGxnOm10LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0QXBpPXtzZXRDYXJvdXNlbEFwaX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRzPXt7IGxvb3A6IHRydWUgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC02IG1iLTMgIG1sLTE2IG1yLTEyIG1heC13LXhzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2tpbGwudGVjaG5pY2FsX3N1Yl9za2lsbHMubWFwKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoc3ViX3NraWxsKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsSXRlbSBrZXk9e3N1Yl9za2lsbC5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCIgcm91bmRlZC1sZyAgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3N1Yl9za2lsbC5zdWJfc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwicC0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN1Yl9za2lsbFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZXRVc2VyVExldmVsKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm9sZU1hcHBpbmcuc2tpbGxfbGV2ZWxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsUHJldmlvdXMgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxOZXh0IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2Fyb3VzZWw+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIE5hdmlnYXRpb24gRG90cyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCIgYm90dG9tLTAgbGVmdC0wIHJpZ2h0LTAgZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgei0yMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiB0b3RhbEl0ZW1zIH0pLm1hcChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChfLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2Nyb2xsVG9JbmRleChpbmRleClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRJbmRleCA9PT0gaW5kZXhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWJsYWNrXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLWdyYXktMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb25JdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7c2tpbGxzTGV2ZWwyICE9PSAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IG10LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwbC0zIHByLTMgcHQtMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHsvKiAvLyBzZXQgdGhlIGxldmVsIGFuZCBzdGFydCB0aGUgbWFwIGhlcmUgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1pbi13LTc4IGJnLWRjY29yYW5nZSB0ZXh0LXdoaXRlIHJvdW5kZWQtdGwteGwgcm91bmRlZC10ci14bCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiZm9udC1zZW1pYm9sZCB0ZXh0LTJ4bCB0ZXh0LWNlbnRlciBwLTAgbWluLXctNzggbWwtMTIgbXItMTJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFN1cHBvcnRlZCBQcmFjdGl0aW9uZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uIHR5cGU9XCJzaW5nbGVcIiBjb2xsYXBzaWJsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyUm9sZSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXJlZFRlY2hTa2lsbHMubWFwKChza2lsbCwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByb2xlTWFwcGluZyA9IHNraWxsLnJvbGVfbWFwcGluZy5maW5kKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKG1hcHBpbmcpID0+IG1hcHBpbmcucm9sZV9pZCA9PT0gdXNlclJvbGUuaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJvbGVNYXBwaW5nLnNraWxsX2xldmVsID09PSAyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uSXRlbVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17c2tpbGwuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2BpdGVtLSR7c2tpbGwuaWR9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBY2NvcmRpb25UcmlnZ2VyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcImJnLWRjY29yYW5nZSB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBwLTMgaG92ZXI6YmctZGNjb3JhbmdlLzg1IHJvdW5kZWQtYi1ub25lIHJvdW5kZWQtdC1ub25lIG1pbi13LTc4XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2tpbGwuc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiB7Z2V0VXNlclRMZXZlbChyb2xlTWFwcGluZy5za2lsbF9sZXZlbCl9ICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgcHgtNSBteC1hdXRvIG10LTUgbWF4LXctN3hsIGxnOm10LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0QXBpPXtzZXRDYXJvdXNlbEFwaX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRzPXt7IGxvb3A6IHRydWUgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC02IG1iLTMgIG1sLTE2IG1yLTEyIG1heC13LXhzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2tpbGwudGVjaG5pY2FsX3N1Yl9za2lsbHMubWFwKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoc3ViX3NraWxsKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsSXRlbSBrZXk9e3N1Yl9za2lsbC5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCIgcm91bmRlZC1sZyAgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3N1Yl9za2lsbC5zdWJfc2tpbGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwicC0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN1Yl9za2lsbFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZXRVc2VyVExldmVsKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm9sZU1hcHBpbmcuc2tpbGxfbGV2ZWxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsUHJldmlvdXMgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxOZXh0IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2Fyb3VzZWw+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIE5hdmlnYXRpb24gRG90cyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCIgYm90dG9tLTAgbGVmdC0wIHJpZ2h0LTAgZmxleCBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgei0yMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiB0b3RhbEl0ZW1zIH0pLm1hcChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChfLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2Nyb2xsVG9JbmRleChpbmRleClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRJbmRleCA9PT0gaW5kZXhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWJsYWNrXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLWdyYXktMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb25JdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IG10LTNcIj5cbiAgICAgICAgICAgICAgICB7c2tpbGxzTGV2ZWwzICE9PSAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IG10LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwbC0zIHByLTMgcHQtMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHsvKiAvLyBzZXQgdGhlIGxldmVsIGFuZCBzdGFydCB0aGUgbWFwIGhlcmUgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1pbi13LTc4IGJnLWRjY2dyZWVuIHRleHQtd2hpdGUgcm91bmRlZC10bC14bCByb3VuZGVkLXRyLXhsIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJmb250LXNlbWlib2xkIHRleHQtMnhsIHRleHQtY2VudGVyIHAtMCBtaW4tdy03OCBtbC0xMiBtci0xMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgSW5kZXBlbmRlbnQgUHJhY3RpdGlvbmVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvbiB0eXBlPVwic2luZ2xlXCIgY29sbGFwc2libGU+XG4gICAgICAgICAgICAgICAgICAgICAgICB7dXNlclJvbGUgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyZWRUZWNoU2tpbGxzLm1hcCgoc2tpbGwsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgcm9sZU1hcHBpbmcgPSBza2lsbC5yb2xlX21hcHBpbmcuZmluZChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChtYXBwaW5nKSA9PiBtYXBwaW5nLnJvbGVfaWQgPT09IHVzZXJSb2xlLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyb2xlTWFwcGluZy5za2lsbF9sZXZlbCA9PT0gMykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvbkl0ZW1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3NraWxsLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtgaXRlbS0ke3NraWxsLmlkfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uVHJpZ2dlclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJiZy1kY2NncmVlbiB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBwLTMgaG92ZXI6YmctZGNjZ3JlZW4vODUgcm91bmRlZC1iLW5vbmUgcm91bmRlZC10LW5vbmUgbWluLXctNzhcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtza2lsbC5za2lsbF9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIHtnZXRVc2VyVExldmVsKHJvbGVNYXBwaW5nLnNraWxsX2xldmVsKX0gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb25UcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBY2NvcmRpb25Db250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBweC01IG14LWF1dG8gbXQtNSBtYXgtdy03eGwgbGc6bXQtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRBcGk9e3NldENhcm91c2VsQXBpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdHM9e3sgbG9vcDogdHJ1ZSB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTYgbWItMyAgbWwtMTYgbXItMTIgbWF4LXcteHNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtza2lsbC50ZWNobmljYWxfc3ViX3NraWxscy5tYXAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChzdWJfc2tpbGwpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxJdGVtIGtleT17c3ViX3NraWxsLmlkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIiByb3VuZGVkLWxnICBtdC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3ViX3NraWxsLnN1Yl9za2lsbF9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJwLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3ViX3NraWxsW1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdldFVzZXJUTGV2ZWwoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb2xlTWFwcGluZy5za2lsbF9sZXZlbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0Nhcm91c2VsSXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0Nhcm91c2VsQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxQcmV2aW91cyAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbE5leHQgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJvdXNlbD5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogTmF2aWdhdGlvbiBEb3RzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIiBib3R0b20tMCBsZWZ0LTAgcmlnaHQtMCBmbGV4IGp1c3RpZnktY2VudGVyIHNwYWNlLXgtMiB6LTIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge0FycmF5LmZyb20oeyBsZW5ndGg6IHRvdGFsSXRlbXMgfSkubWFwKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKF8sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY3JvbGxUb0luZGV4KGluZGV4KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTMgaC0zIHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudEluZGV4ID09PSBpbmRleFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctYmxhY2tcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYmctZ3JheS0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb25Db250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvbkl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIHtza2lsbHNMZXZlbDQgIT09IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInBsLTMgcHItMyBwdC0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgey8qIC8vIHNldCB0aGUgbGV2ZWwgYW5kIHN0YXJ0IHRoZSBtYXAgaGVyZSAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwibWluLXctNzggYmctZGNjbGlnaHRibHVlIHRleHQtd2hpdGUgcm91bmRlZC10bC14bCByb3VuZGVkLXRyLXhsIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJmb250LXNlbWlib2xkIHRleHQtMnhsIHRleHQtY2VudGVyIHAtMCBtaW4tdy03OCBtbC0xMiBtci0xMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgRXhwZXJ0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvbiB0eXBlPVwic2luZ2xlXCIgY29sbGFwc2libGU+XG4gICAgICAgICAgICAgICAgICAgICAgICB7dXNlclJvbGUgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyZWRUZWNoU2tpbGxzLm1hcCgoc2tpbGwsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgcm9sZU1hcHBpbmcgPSBza2lsbC5yb2xlX21hcHBpbmcuZmluZChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChtYXBwaW5nKSA9PiBtYXBwaW5nLnJvbGVfaWQgPT09IHVzZXJSb2xlLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyb2xlTWFwcGluZy5za2lsbF9sZXZlbCA9PT0gNCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvbkl0ZW1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3NraWxsLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtgaXRlbS0ke3NraWxsLmlkfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWNjb3JkaW9uVHJpZ2dlclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJiZy1kY2NsaWdodGJsdWUgdGV4dC13aGl0ZSBmb250LWJvbGQgcC0zIGhvdmVyOmJnLWRjY2xpZ2h0Ymx1ZS84NSByb3VuZGVkLWItbm9uZSByb3VuZGVkLXQtbm9uZSBtaW4tdy03OFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NraWxsLnNraWxsX25hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyoge2dldFVzZXJUTGV2ZWwocm9sZU1hcHBpbmcuc2tpbGxfbGV2ZWwpfSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvblRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvbkNvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHB4LTUgbXgtYXV0byBtdC01IG1heC13LTd4bCBsZzptdC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEFwaT17c2V0Q2Fyb3VzZWxBcGl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0cz17eyBsb29wOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNiBtYi0zICBtbC0xNiBtci0xMiBtYXgtdy14c1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2Fyb3VzZWxDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NraWxsLnRlY2huaWNhbF9zdWJfc2tpbGxzLm1hcChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHN1Yl9za2lsbCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbEl0ZW0ga2V5PXtzdWJfc2tpbGwuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiIHJvdW5kZWQtbGcgIG10LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdWJfc2tpbGwuc3ViX3NraWxsX25hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInAtMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdWJfc2tpbGxbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2V0VXNlclRMZXZlbChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvbGVNYXBwaW5nLnNraWxsX2xldmVsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2Fyb3VzZWxJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2Fyb3VzZWxDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJvdXNlbFByZXZpb3VzIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcm91c2VsTmV4dCAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0Nhcm91c2VsPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIERvdHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiIGJvdHRvbS0wIGxlZnQtMCByaWdodC0wIGZsZXgganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yIHotMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogdG90YWxJdGVtcyB9KS5tYXAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoXywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjcm9sbFRvSW5kZXgoaW5kZXgpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50SW5kZXggPT09IGluZGV4XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ibGFja1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1ncmF5LTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvbkNvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uSXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICAgICAgPC9UYWJzPlxuICAgICAgICApfVxuICAgICAgPC9TaWRlYmFySW5zZXQ+XG4gICAgICB7LyogPG1haW4+e2NoaWxkcmVufTwvbWFpbj4gKi99XG4gICAgPC9TaWRlYmFyUHJvdmlkZXI+XG4gICk7XG59XG5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQcm9wcyA9IGFzeW5jICgpID0+IHtcbiAgY29uc3QgYmRhdGEgPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKFwiYmVoYXZpb3VyYWxfc2tpbGxzXCIpXG4gICAgLnNlbGVjdChcbiAgICAgIFwiaWQsIGdyb3VwLCBza2lsbF9uYW1lLCBiZWhhdmlvdXJhbF9zdWJfc2tpbGxzIChpZCwgc3ViX3NraWxsX25hbWUsIGxldmVsXzFfZGVzY3JpcHRpb24sIGxldmVsXzJfZGVzY3JpcHRpb24sIGxldmVsXzNfZGVzY3JpcHRpb24sIGxldmVsXzRfZGVzY3JpcHRpb24sIGxldmVsXzVfZGVzY3JpcHRpb24pXCJcbiAgICApO1xuXG4gIGNvbnN0IHRlY2hkYXRhID0gYXdhaXQgc3VwYWJhc2VcbiAgICAuZnJvbShcInRlY2huaWNhbF9za2lsbHNcIilcbiAgICAuc2VsZWN0KFxuICAgICAgXCJpZCwgc2tpbGxfbmFtZSwgcm9sZV9tYXBwaW5nIChpZCwgcm9sZV9pZCwgc2tpbGxfbGV2ZWwpLCB0ZWNobmljYWxfc3ViX3NraWxscyAoaWQsIHN1Yl9za2lsbF9uYW1lLCBsZXZlbF8xX2Rlc2NyaXB0aW9uLCBsZXZlbF8yX2Rlc2NyaXB0aW9uLCBsZXZlbF8zX2Rlc2NyaXB0aW9uLCBsZXZlbF80X2Rlc2NyaXB0aW9uKVwiXG4gICAgKTtcblxuICBjb25zdCByb2xlc2RhdGEgPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKFwicm9sZXNcIilcbiAgICAuc2VsZWN0KFwiaWQsIGZ1bmN0aW9uLCB0ZWFtLCByb2xlX25hbWVcIik7XG5cbiAgY29uc3QgcmVzcG9uc2VzID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgIGJkYXRhLFxuICAgIHRlY2hkYXRhLFxuICAgIHJvbGVzZGF0YSxcbiAgXSk7XG5cbiAgY29uc29sZS5sb2cocm9sZXNkYXRhKTtcblxuICByZXR1cm4ge1xuICAgIHByb3BzOiB7XG4gICAgICBiZWhhdmlvdXJhbF9za2lsbHM6IHJlc3BvbnNlc1swXS5kYXRhLFxuICAgICAgdGVjaG5pY2FsX3NraWxsczogcmVzcG9uc2VzWzFdLmRhdGEsXG4gICAgICByb2xlX2RhdGE6IHJlc3BvbnNlc1syXS5kYXRhLFxuICAgIH0sXG4gIH07XG59O1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwiSW1hZ2UiLCJzdXBhYmFzZSIsIk92YWwiLCJTaWRlYmFyUHJvdmlkZXIiLCJTaWRlYmFyVHJpZ2dlciIsIlNpZGViYXJJbnNldCIsIlNlcGFyYXRvciIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRGb290ZXIiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQWNjb3JkaW9uIiwiQWNjb3JkaW9uQ29udGVudCIsIkFjY29yZGlvbkl0ZW0iLCJBY2NvcmRpb25UcmlnZ2VyIiwiQ2Fyb3VzZWwiLCJDYXJvdXNlbENvbnRlbnQiLCJDYXJvdXNlbEl0ZW0iLCJDYXJvdXNlbE5leHQiLCJDYXJvdXNlbFByZXZpb3VzIiwiQ2Fyb3VzZWxBcGkiLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2FwdGlvbiIsIlRhYmxlQ2VsbCIsIlRhYmxlSGVhZCIsIlRhYmxlSGVhZGVyIiwiVGFibGVSb3ciLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsIlNlbGVjdExhYmVsIiwiU2VsZWN0R3JvdXAiLCJUYWJzIiwiVGFic0NvbnRlbnQiLCJUYWJzTGlzdCIsIlRhYnNUcmlnZ2VyIiwiQnV0dG9uIiwiQmFkZ2UiLCJza2lsbHNDaGFydCIsIkJyZWFkY3J1bWIiLCJCcmVhZGNydW1iSXRlbSIsIkJyZWFkY3J1bWJMaW5rIiwiQnJlYWRjcnVtYkxpc3QiLCJCcmVhZGNydW1iU2VwYXJhdG9yIiwiUG9wb3ZlciIsIlBvcG92ZXJDb250ZW50IiwiUG9wb3ZlclRyaWdnZXIiLCJjbiIsIkNvbW1hbmQiLCJDb21tYW5kRW1wdHkiLCJDb21tYW5kR3JvdXAiLCJDb21tYW5kSW5wdXQiLCJDb21tYW5kSXRlbSIsIkNvbW1hbmRMaXN0IiwiQ2hlY2siLCJDaGV2cm9uc1VwRG93biIsIkNpcmNsZUNoZWNrIiwiQ2hldnJvblJpZ2h0IiwiQ2hldnJvbkxlZnQiLCJMYWJlbCIsIkFwcFNpZGViYXIiLCJSb2xlVGVjaFNraWxsU3VtbWFyeSIsImZ1bmN0aW9ucyIsIm5hbWUiLCJ2YWx1ZSIsInRlYW1zIiwiRGFzaGJvYXJkIiwiYmVoYXZpb3VyYWxfc2tpbGxzIiwidGVjaG5pY2FsX3NraWxscyIsInRlY2hfZGF0YV91c2VyIiwicm9sZV9kYXRhIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzZXNzaW9uIiwic2V0U2Vzc2lvbiIsInVzZXJEYXRhIiwic2V0VXNlckRhdGEiLCJ1c2VyUm9sZSIsInNldFVzZXJSb2xlIiwic2VsZWN0ZWRGdW5jdGlvbiIsInNldFNlbGVjdGVkRnVuY3Rpb24iLCJzZWxlY3RlZFRlYW0iLCJzZXRTZWxlY3RlZFRlYW0iLCJzZWxlY3RlZFJvbGUiLCJzZXRTZWxlY3RlZFJvbGUiLCJzZWxlY3RlZFJvbGVOYW1lIiwic2V0U2VsZWN0ZWRSb2xlTmFtZSIsInNlbGVjdGVkUm9sZUlkIiwic2V0U2VsZWN0ZWRSb2xlSWQiLCJmaWx0ZXJlZFJvbGVzIiwic2V0RmlsdGVyZWRSb2xlcyIsIm9wZW4iLCJzZXRPcGVuIiwidXNlckJMZXZlbCIsInNldFVzZXJCTGV2ZWwiLCJ1c2VyQkxldmVsRGlzcGxheSIsInNldFVzZXJCTGV2ZWxEaXNwbGF5IiwidXNlclJvbGVEZXNjIiwic2V0VXNlclJvbGVEZXNjIiwiZmlsdGVyZWRUZWNoU2tpbGxzIiwic2V0RmlsdGVyZWRUZWNoU2tpbGxzIiwic2hvd0JTa2lsbHMiLCJzZXRTaG93QlNraWxscyIsInNob3dUU2tpbGxzIiwic2V0U2hvd1RTa2lsbHMiLCJzaG93VGFiVmlldyIsInNldFNob3dUYWJWaWV3IiwiY2Fyb3VzZWxBcGkiLCJzZXRDYXJvdXNlbEFwaSIsImN1cnJlbnRJbmRleCIsInNldEN1cnJlbnRJbmRleCIsInRvdGFsSXRlbXMiLCJzZXRUb3RhbEl0ZW1zIiwidG90YWxJdGVtc1RoaW5rIiwic2V0VG90YWxJdGVtc1RoaW5rIiwidG90YWxJdGVtc0V4ZWN1dGUiLCJzZXRUb3RhbEl0ZW1zRXhlY3V0ZSIsInRvdGFsSXRlbXNHcm93Iiwic2V0VG90YWxJdGVtc0dyb3ciLCJza2lsbHNMZXZlbDEiLCJzZXRTa2lsbHNMZXZlbDEiLCJza2lsbHNMZXZlbDIiLCJzZXRTa2lsbHNMZXZlbDIiLCJza2lsbHNMZXZlbDMiLCJzZXRTa2lsbHNMZXZlbDMiLCJza2lsbHNMZXZlbDQiLCJzZXRTa2lsbHNMZXZlbDQiLCJyb3V0ZXIiLCJhdXRoIiwiZ2V0U2Vzc2lvbiIsInRoZW4iLCJkYXRhIiwicHVzaCIsInN1YnNjcmlwdGlvbiIsIm9uQXV0aFN0YXRlQ2hhbmdlIiwiX2V2ZW50IiwidW5zdWJzY3JpYmUiLCJnZXRVc2VyUHJvZmlsZSIsInVwZGF0ZUNhcm91c2VsU3RhdGUiLCJzZWxlY3RlZFNjcm9sbFNuYXAiLCJzY3JvbGxTbmFwTGlzdCIsImxlbmd0aCIsIm9uIiwib2ZmIiwic2Nyb2xsVG9JbmRleCIsImluZGV4Iiwic2Nyb2xsVG8iLCJjb25zb2xlIiwibG9nIiwidXNlciIsImdldFVzZXIiLCJFcnJvciIsImVycm9yIiwic3RhdHVzIiwiZnJvbSIsInNlbGVjdCIsImVxIiwiaWQiLCJzaW5nbGUiLCJtZXNzYWdlIiwiZ2V0VGhlU2VsZWN0ZWRSb2xlIiwicm9sZV9pZCIsImdldFVzZXJCTGV2ZWwiLCJiZWhhdmlvdXJhbF9za2lsbF9sZXZlbCIsInJvbGVfZGVzY3JpcHRpb24iLCJnZXRTZWxlY3RlZFJvbGUiLCJzZWxlY3RlZF9pZCIsImZpbHRlcmVkVGVjaCIsImZpbHRlciIsInJvbGVfbWFwcGluZyIsInNvbWUiLCJuIiwic2tpbGxzMSIsInNraWxsX2xldmVsIiwic2tpbGxzMiIsInNraWxsczMiLCJza2lsbHM0IiwibGV2ZWwiLCJnZXRVc2VyVExldmVsIiwiZmlsdGVyVGhlUm9sZXMiLCJ0ZWFtX2lkIiwiZWwiLCJ0ZWFtIiwiZGl2IiwiY2xhc3NOYW1lIiwic3Ryb2tlIiwiaGVhZGVyIiwib3JpZW50YXRpb24iLCJocmVmIiwicm9sZV9uYW1lIiwib25WYWx1ZUNoYW5nZSIsImZ1bmNzZWxlY3RlZCIsInBsYWNlaG9sZGVyIiwibWFwIiwiZnVuYyIsImRpc2FibGVkIiwidGVhbXNlbGVjdGVkIiwib25PcGVuQ2hhbmdlIiwiYXNDaGlsZCIsInZhcmlhbnQiLCJyb2xlIiwiYXJpYS1leHBhbmRlZCIsIm9uU2VsZWN0IiwiZGVmYXVsdFZhbHVlIiwic3JjIiwiaGVpZ2h0IiwiYWx0IiwicCIsInR5cGUiLCJjb2xsYXBzaWJsZSIsInNraWxsIiwiZ3JvdXAiLCJza2lsbF9uYW1lIiwic2V0QXBpIiwib3B0cyIsImxvb3AiLCJiZWhhdmlvdXJhbF9zdWJfc2tpbGxzIiwic3ViX3NraWxsIiwic3ViX3NraWxsX25hbWUiLCJBcnJheSIsIl8iLCJidXR0b24iLCJvbkNsaWNrIiwiY2xhc3MiLCJyb2xlTWFwcGluZyIsImZpbmQiLCJtYXBwaW5nIiwidGVjaG5pY2FsX3N1Yl9za2lsbHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/teams.js\n"));

/***/ })

});