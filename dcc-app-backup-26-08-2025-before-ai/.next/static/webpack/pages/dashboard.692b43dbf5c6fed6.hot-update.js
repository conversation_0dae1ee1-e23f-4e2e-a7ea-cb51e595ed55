"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./core/components/app-sidebar.jsx":
/*!*****************************************!*\
  !*** ./core/components/app-sidebar.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookUser,Cog,LayoutDashboard,Store,UserSquare,UsersIcon!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=BookUser,Cog,LayoutDashboard,Store,UserSquare,UsersIcon!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_team_switcher__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @core/components/ui/team-switcher */ \"(pages-dir-browser)/./core/components/ui/team-switcher.jsx\");\n/* harmony import */ var _core_components_ui_nav_main__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/components/ui/nav-main */ \"(pages-dir-browser)/./core/components/ui/nav-main.jsx\");\n/* harmony import */ var _core_components_ui_nav_tech__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/nav-tech */ \"(pages-dir-browser)/./core/components/ui/nav-tech.jsx\");\n/* harmony import */ var _core_components_ui_nav_projects__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/nav-projects */ \"(pages-dir-browser)/./core/components/ui/nav-projects.jsx\");\n/* harmony import */ var _core_components_ui_nav_teams__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/ui/nav-teams */ \"(pages-dir-browser)/./core/components/ui/nav-teams.jsx\");\n/* harmony import */ var _core_components_ui_nav_role__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/nav-role */ \"(pages-dir-browser)/./core/components/ui/nav-role.jsx\");\n/* harmony import */ var _core_components_ui_nav_dashboard__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/nav-dashboard */ \"(pages-dir-browser)/./core/components/ui/nav-dashboard.jsx\");\n/* harmony import */ var _core_components_ui_nav_admin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/components/ui/nav-admin */ \"(pages-dir-browser)/./core/components/ui/nav-admin.jsx\");\n/* harmony import */ var _core_components_ui_nav_resources__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/nav-resources */ \"(pages-dir-browser)/./core/components/ui/nav-resources.jsx\");\n/* harmony import */ var _core_components_ui_nav_user__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/nav-user */ \"(pages-dir-browser)/./core/components/ui/nav-user.jsx\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Menu items.\nconst items = [\n    {\n        title: \"Skills Library\",\n        url: \"#\",\n        icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__.LayoutDashboard,\n        isActive: true,\n        items: [\n            {\n                title: \"History\",\n                url: \"#\"\n            },\n            {\n                title: \"Starred\",\n                url: \"#\"\n            },\n            {\n                title: \"Settings\",\n                url: \"#\"\n            }\n        ]\n    }\n];\nconst data = {\n    dashboard: [\n        {\n            name: \"Dashboard\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__.LayoutDashboard\n        }\n    ],\n    role: [\n        {\n            name: \"Roles\",\n            url: \"/roles\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__.BookUser\n        }\n    ],\n    teams: [\n        {\n            name: \"Teams\",\n            url: \"/teams\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__.UsersIcon\n        }\n    ],\n    resources: [\n        {\n            name: \"Talent Marketplace\",\n            url: \"/talent-marketplace\",\n            icon: _barrel_optimize_names_BookUser_Cog_LayoutDashboard_Store_UserSquare_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__.Store\n        }\n    ]\n};\n// const algorithm = [\n//   \"Cyber Security Operations\",\n//   \"Security Architecture and Assurance\",\n// ];\n// const language = [\n//   \"Security Compliance, Risk and Resilience\",\n//   \"Security, Demand, Capability and Awareness\",\n// ];\nfunction AppSidebar(param) {\n    let { userData, behavioural_skills, technical_skills, selected_item, selected_item_tech, allMappings, deleteMappingRecord, typeSelected } = param;\n    _s();\n    const { state, open, setOpen, openMobile, setOpenMobile, isMobile, toggleSidebar } = (0,_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teamSelected, setTeamSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSkills, setShowSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [typeSelected, setTypeSelected] = useState(\"\");\n    const changeSelectOptionHandler = (value)=>{\n        setSelected(value);\n        setShowSkills(false);\n    };\n    const teamSelectOptionHandler = (value)=>{\n        setTeamSelected(value);\n        setShowSkills(true);\n    };\n    const typeSelector = (value)=>{\n        props.handleShowSkills(value);\n    };\n    /* --- DEBUG --- */ // console.log(\"userData\", userData);\n    // console.log(\"behavioural_skills\", behavioural_skills);\n    // console.log(\"selected_item\", selected_item);\n    /* --- DEBUG --- */ /** Type variable to store different array for different dropdown */ let type = null;\n    /** This will be used to create set of options that user will see */ let options = null;\n    /** Setting Type variable according to dropdown */ if (selected === \"Security\") {\n        type = algorithm;\n    } else if (selected === \"Another Security\") {\n        type = language;\n    }\n    /** If \"Type\" is null or undefined then options will be null,\n   * otherwise it will create a options iterable based on our array\n   */ if (type) {\n        options = type.map((el)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_16__.SelectItem, {\n                value: el,\n                children: el\n            }, el, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this));\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n        collapsible: \"offcanvas\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_team_switcher__WEBPACK_IMPORTED_MODULE_4__.TeamSwitcher, {\n                    teams: data.teams\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_dashboard__WEBPACK_IMPORTED_MODULE_10__.NavDashboard, {\n                        projects: data.dashboard\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_main__WEBPACK_IMPORTED_MODULE_5__.NavMain, {\n                        items: behavioural_skills,\n                        selected_item: selected_item\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_tech__WEBPACK_IMPORTED_MODULE_6__.NavTech, {\n                        items: technical_skills,\n                        selected_item_tech: selected_item_tech\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_role__WEBPACK_IMPORTED_MODULE_9__.NavRole, {\n                        projects: data.role\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_teams__WEBPACK_IMPORTED_MODULE_8__.NavTeams, {\n                        projects: data.teams\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_nav_user__WEBPACK_IMPORTED_MODULE_13__.NavUser, {\n                    user: userData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarRail, {}, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/components/app-sidebar.jsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"aB8fc4ANDxOoy4nbqDQatry3ZNE=\", false, function() {\n    return [\n        _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/components/app-sidebar.jsx\n"));

/***/ })

});