"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/teams",{

/***/ "(pages-dir-browser)/./core/Teams/TeamTSkills.js":
/*!***********************************!*\
  !*** ./core/Teams/TeamTSkills.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @core/components/ui/card */ \"(pages-dir-browser)/./core/components/ui/card.jsx\");\n\n\nconst TeamTSkills = (param)=>{\n    let { technical_skills, role_ids, team_mappings } = param;\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    // console.log(\"role_ids\");\n    // console.log(role_ids);\n    // console.log(\"team_mappings\");\n    // console.log(team_mappings);\n    /*\n\n    - role ids for the selected team (extract and save in array)\n    - query role mapping for the role ids (multi query)\n    - get the technical skills for the role mappings\n\n\n ----- \n\n\n\n */ const return_skill_level = (lvl)=>{\n        const filteredTech = technical_skills.filter((el)=>el.role_mapping.filter((n)=>n.role_id === lvl).length > 0);\n        console.log(\"filteredTech\");\n        console.log(filteredTech && filteredTech.role_mapping[0].s);\n        // const hasMatchingRole = skill.role_mapping.filter((mapping) => {\n        //   role_ids.filter((role) => role.id === mapping.role_id);\n        //   console.log(skill);\n        // });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"test\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n            lineNumber: 40,\n            columnNumber: 12\n        }, undefined);\n    };\n    const return_levels = (technical_skills, role_ids)=>{\n        console.log(\"all skills\");\n        technical_skills.map((skill)=>{\n            const hasMatchingRole = skill.role_mapping.some((mapping)=>{\n                role_ids.some((role)=>role.id === mapping.role_id);\n                console.log(skill);\n            });\n            return hasMatchingRole ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"test\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                lineNumber: 52,\n                columnNumber: 32\n            }, undefined) : null;\n        });\n    };\n    const skills_header = (technical_skills, role_ids)=>{\n        return technical_skills.map((skill)=>{\n            const hasMatchingRole = skill.role_mapping.some((mapping)=>role_ids.some((role)=>role.id === mapping.role_id));\n            return hasMatchingRole ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                className: \"min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                    className: \"font-semibold text-xs text-center p-1\",\n                    children: skill.skill_name\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, undefined)\n            }, skill.id, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined) : null;\n        });\n    };\n    const skill_rows = (technical_skills, role_ids)=>{\n        return technical_skills.map((skill)=>{\n            const hasMatchingRole = skill.role_mapping.some((mapping)=>role_ids.some((role)=>role.id === mapping.role_id));\n            return hasMatchingRole ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"font-bold text-sm text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block align-middle\",\n                                children: role_ids[0].role_name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"text-xs text-center tracking-tight p-0\",\n                            children: \"Independent Practitioner\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true) : null;\n        });\n    };\n    const skill_rows2 = (technical_skills, role_ids)=>{\n        return role_ids.map((role, index)=>{\n            // console.log(role.role_name);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"font-bold text-sm text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block align-middle\",\n                                children: role.role_name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"text-xs text-center tracking-tight p-0\",\n                            children: \"Independent Practitioner\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true);\n        });\n    // return technical_skills.map((skill) => {\n    //   const hasMatchingRole = skill.role_mapping.some((mapping) =>\n    //     role_ids.some((role) => role.id === mapping.role_id)\n    //   );\n    //   return hasMatchingRole ? (\n    //     <>\n    //       <Card className=\"min-w-8  bg-[#1f144a] text-primary-foreground\">\n    //         <CardContent className={\"font-bold text-sm text-center\"}>\n    //           <div className=\"inline-block align-middle\">\n    //             {role_ids[0].role_name}\n    //           </div>\n    //         </CardContent>\n    //       </Card>\n    //       <Card className=\"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\">\n    //         <CardContent className=\"text-xs text-center tracking-tight p-0\">\n    //           Independent Practitioner\n    //         </CardContent>\n    //       </Card>\n    //     </>\n    //   ) : null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                className: \"min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                    className: \"font-extrabold text-lg text-center\",\n                    children: \"Skills\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            skills_header(technical_skills, role_ids),\n            skill_rows2(technical_skills, role_ids),\n            return_skill_level(4)\n        ]\n    }, void 0, true);\n//   const getLevel = (lvl) => {\n//     switch (lvl) {\n//       case 1:\n//         return \"Operational Contributor\";\n//       case 2:\n//         return \"Advanced Contributor\";\n//       case 3:\n//         return \"Team Leader\";\n//       case 4:\n//         return \"Leader of Leaders\";\n//       case 5:\n//         return \"Organisational Leader\";\n//     }\n//   };\n//   return (\n//     <>\n//       {\" \"}\n//       <Card className=\"min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-extrabold text-lg text-center\"}>\n//           Skills\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           BCDR\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Change Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Certification Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold  text-xs text-center p-1\"}>\n//           Compliance & Regulatory Assurance\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold  text-xs text-center p-1\"}>\n//           Data Analytics and Insights\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Development Lifecycle\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Incident Response Lifecycle\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Infrastructure and Cloud Computing\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold  text-xs text-center p-1\"}>\n//           Procurement\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Risk Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Supplier Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Threat Intelligence\n//         </CardContent>\n//       </Card>\n//     </>\n//   );\n//     filteredBSkills &&\n//     filteredBSkills.map((role) => {\n//       if (role.role_name === \"Team Owner\") {\n//         return null;\n//       }\n//       return (\n//         <>\n//           <Card className=\"min-w-8  bg-[#1f144a] text-primary-foreground \">\n//             <CardContent className={\"font-semibold text-sm text-center\"}>\n//               <div className=\"inline-block align-middle\">{role.role_name}</div>\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight] \">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//         </>\n//       );\n//     })\n//   );\n};\n_c = TeamTSkills;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TeamTSkills);\nvar _c;\n$RefreshReg$(_c, \"TeamTSkills\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/Teams/TeamTSkills.js\n"));

/***/ })

});