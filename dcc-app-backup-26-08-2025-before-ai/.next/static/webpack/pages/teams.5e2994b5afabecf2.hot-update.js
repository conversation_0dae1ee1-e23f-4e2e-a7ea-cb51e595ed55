"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/teams",{

/***/ "(pages-dir-browser)/./pages/teams.js":
/*!************************!*\
  !*** ./pages/teams.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(pages-dir-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/separator */ \"(pages-dir-browser)/./core/components/ui/separator.jsx\");\n/* harmony import */ var _core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/ui/card */ \"(pages-dir-browser)/./core/components/ui/card.jsx\");\n/* harmony import */ var _core_components_ui_accordion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/accordion */ \"(pages-dir-browser)/./core/components/ui/accordion.jsx\");\n/* harmony import */ var _core_components_ui_carousel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/carousel */ \"(pages-dir-browser)/./core/components/ui/carousel.jsx\");\n/* harmony import */ var _core_components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/components/ui/table */ \"(pages-dir-browser)/./core/components/ui/table.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n/* harmony import */ var _core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @core/components/ui/tabs */ \"(pages-dir-browser)/./core/components/ui/tabs.jsx\");\n/* harmony import */ var _core_components_ui_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @core/components/ui/button */ \"(pages-dir-browser)/./core/components/ui/button.jsx\");\n/* harmony import */ var _core_components_ui_badge__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @core/components/ui/badge */ \"(pages-dir-browser)/./core/components/ui/badge.jsx\");\n/* harmony import */ var _public_skills_chart_png__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../../../../../../../../public/skills-chart.png */ \"(pages-dir-browser)/./public/skills-chart.png\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_ui_popover__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @core/components/ui/popover */ \"(pages-dir-browser)/./core/components/ui/popover.jsx\");\n/* harmony import */ var _core_lib_utils__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @core/lib/utils */ \"(pages-dir-browser)/./core/lib/utils.js\");\n/* harmony import */ var _core_components_ui_command__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @core/components/ui/command */ \"(pages-dir-browser)/./core/components/ui/command.jsx\");\n/* harmony import */ var _core_components_ui_label__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @core/components/ui/label */ \"(pages-dir-browser)/./core/components/ui/label.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n/* harmony import */ var _core_Teams_TeamBSkills__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @core/Teams/TeamBSkills */ \"(pages-dir-browser)/./core/Teams/TeamBSkills.js\");\n/* harmony import */ var _core_Teams_TeamBSkillsEx__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @core/Teams/TeamBSkillsEx */ \"(pages-dir-browser)/./core/Teams/TeamBSkillsEx.js\");\n/* harmony import */ var _core_Teams_TeamBSkillsGrw__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @core/Teams/TeamBSkillsGrw */ \"(pages-dir-browser)/./core/Teams/TeamBSkillsGrw.js\");\n/* harmony import */ var _core_Teams_TeamTSkills__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @core/Teams/TeamTSkills */ \"(pages-dir-browser)/./core/Teams/TeamTSkills.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst functions = [\n    {\n        name: \"Security\",\n        value: \"Security\"\n    }\n];\nconst teams = [\n    {\n        name: \"Cyber Security Operations\",\n        value: \"Cyber Security Operations\"\n    }\n];\nconst types = [\n    {\n        name: \"Behavioural skills\",\n        value: \"Behavioural\"\n    },\n    {\n        name: \"Technical skills\",\n        value: \"Technical\"\n    }\n];\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills, tech_data_user, role_data } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedFunction, setSelectedFunction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTeam, setSelectedTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [roleIds, setRoleIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTeaData, setSelectedTeamData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userBLevel, setUserBLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userBLevelDisplay, setUserBLevelDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRoleDesc, setUserRoleDesc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filteredTechSkills, setFilteredTechSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBSkills, setShowBSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTSkills, setShowTSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTabView, setShowTabView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [carouselApi, setCarouselApi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsThink, setTotalItemsThink] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsExecute, setTotalItemsExecute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalItemsGrow, setTotalItemsGrow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [skillsLevel1, setSkillsLevel1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel2, setSkillsLevel2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel3, setSkillsLevel3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [skillsLevel4, setSkillsLevel4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!carouselApi) return;\n            const updateCarouselState = {\n                \"Dashboard.useEffect.updateCarouselState\": ()=>{\n                    setCurrentIndex(carouselApi.selectedScrollSnap());\n                    setTotalItems(carouselApi.scrollSnapList().length);\n                }\n            }[\"Dashboard.useEffect.updateCarouselState\"];\n            updateCarouselState();\n            carouselApi.on(\"select\", updateCarouselState);\n            return ({\n                \"Dashboard.useEffect\": ()=>{\n                    carouselApi.off(\"select\", updateCarouselState); // Clean up on unmount\n                }\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        carouselApi\n    ]);\n    const scrollToIndex = (index)=>{\n        carouselApi === null || carouselApi === void 0 ? void 0 : carouselApi.scrollTo(index);\n    };\n    /* --- DEBUG --- */ //   console.log(behavioural_skills);\n    //   console.log(\"user data\");\n    //   console.log(userData);\n    //   console.log(\"user role\");\n    //   console.log(userRole);\n    //   console.log(\"selectedTeam\");\n    //   console.log(selectedTeam);\n    //   console.log(\"selectedType\");\n    //   console.log(selectedType);\n    //   console.log(\"selectedRole\");\n    //   console.log(selectedRole);\n    //   console.log(\"role_data\");\n    //   console.log(role_data);\n    //   console.log(\"selectedFunction\");\n    //   console.log(selectedFunction);\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get team roles\n    async function getTheSelectedTeam(team) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name, behavioural_skill_level, role_description\").eq(\"team\", team).select(\"*\");\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                // console.log(\"team selected\");\n                // console.log(data);\n                setSelectedTeamData(data);\n                let role_ids = data.map((param)=>{\n                    let { id } = param;\n                    return id;\n                });\n                // let role_ids = data.map(({ id, role_name }) => ({\n                //   id,\n                //   role_name,\n                // }));\n                setRoleIds(role_ids);\n            // console.log(\"team ids\");\n            // console.log(team_ids);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // get team roles\n    async function getTeamMappedRoles(team) {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from(\"roles\").select(\"id, team, role_name, behavioural_skill_level, role_description\").eq(\"team\", team).select(\"*\");\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                // console.log(\"team selected\");\n                // console.log(data);\n                setSelectedTeamData(data);\n                let team_ids = data.map((param)=>{\n                    let { id } = param;\n                    return id;\n                });\n                console.log(\"team ids\");\n                console.log(team_ids);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    const getTeam = (team)=>{\n        getTheSelectedTeam(team);\n    };\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_5__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 374,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                    lineNumber: 373,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 372,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n            lineNumber: 371,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_22__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbLink, {\n                                                    href: \"#\",\n                                                    children: \"Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_17__.BreadcrumbLink, {\n                                                    children: [\n                                                        selectedTeam ? selectedTeam : \"Select a team\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row gap-2 p-2 pt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex m-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-1 text-md text-center pb-1 text-primary\",\n                                    children: \"Select a tean to view the Team Skills\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        onValueChange: (funcselected)=>setSelectedFunction(funcselected),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-[250px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: selectedFunction || \"Select Function\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectGroup, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectLabel, {\n                                                            children: \"Function\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        functions.map((func)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: func.name,\n                                                                children: func.name\n                                                            }, func.name, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        disabled: !selectedFunction,\n                                        onValueChange: (teamselected)=>{\n                                            setSelectedTeam(teamselected);\n                                        //   filterTheRoles(teamselected);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-[250px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: selectedTeam || \"Select Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectGroup, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectLabel, {\n                                                            children: \"Team\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        teams.map((team)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: team.name,\n                                                                children: team.name\n                                                            }, team.name, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6 pt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        disabled: !selectedTeam,\n                                        onValueChange: (typeSelected)=>{\n                                            setSelectedType(typeSelected);\n                                            getTeam(selectedTeam);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-[250px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: selectedType || \"Select type\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectGroup, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectLabel, {\n                                                            children: \"Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        types.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: type.value,\n                                                                children: type.name\n                                                            }, type.value, false, {\n                                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, this),\n                    selectedType == \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                        defaultValue: \"think\",\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsList, {\n                                className: \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                        value: \"think\",\n                                        className: \"bg-[#ca005d] text-white font-extrabold text-md w-[50px] h-[50px] rounded-tr-xl rounded-tl-xl rounded-br-none  rounded-bl-none\",\n                                        children: \"Think\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                        value: \"execute\",\n                                        className: \"bg-[#861889] text-white font-extrabold text-md w-[150px] h-[50px] rounded-tr-xl rounded-tl-xl rounded-br-none  rounded-bl-none\",\n                                        children: \"Execute\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                        value: \"grow\",\n                                        className: \"bg-[#5C2071] text-white font-extrabold text-md w-[150px] h-[50px] rounded-tr-xl rounded-tl-xl rounded-br-none  rounded-bl-none\",\n                                        children: \"Grow\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 503,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                value: \"think\",\n                                children: selectedType == \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-1 flex-col gap-0 pl-1 pr-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-[25%_15%_15%_15%_15%_15%]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8 bg-[#ca005d] text-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-extrabold text-lg text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"m-auto\",\n                                                            children: \"Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 531,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8  bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold text-sm text-center p-0\",\n                                                    children: \"Strategic Thinking\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 540,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8  bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold text-sm text-center p-0\",\n                                                    children: \"Purposeful Planning\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 547,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8  bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold text-sm text-center p-0\",\n                                                    children: \"Shaping Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 554,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Customer Focus\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 561,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#ca005d] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center p-0\",\n                                                    children: \"Agile and Adaptable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 568,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Teams_TeamBSkills__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                filteredBSkills: selectedTeaData && selectedTeaData\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 578,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 529,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 528,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 526,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                value: \"execute\",\n                                children: selectedType == \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-1 flex-col gap-0 pl-1 pr-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-[25%_18.7%_18.7%_18.7%_18.7%]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8 bg-[#861889] text-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-extrabold text-lg text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"m-auto\",\n                                                            children: \"Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 593,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#861889] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Engage and Influence\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 607,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#861889] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Deliver Results\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 615,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#861889] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Collaborate Openly\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 623,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#861889] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Trust and Integrity\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 631,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Teams_TeamBSkillsEx__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                filteredBSkills: selectedTeaData && selectedTeaData\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 641,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 591,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 590,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                                value: \"grow\",\n                                children: selectedType == \"Behavioural\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-1 flex-col gap-0 pl-1 pr-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-[25%_25%_25%_25%]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8 bg-[#5c2071] text-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-extrabold text-lg text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"m-auto\",\n                                                            children: \"Skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 656,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#5c2071] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Develop Self\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 670,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#5c2071] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Enable Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 678,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"min-w-8   bg-[#5c2071] text-primary-foreground\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                    className: \"font-semibold  text-sm text-center\",\n                                                    children: \"Develop Others\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 686,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Teams_TeamBSkillsGrw__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                filteredBSkills: selectedTeaData && selectedTeaData\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 696,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 654,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 653,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 651,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 501,\n                        columnNumber: 11\n                    }, this),\n                    selectedType == \"Technical\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col gap-2 p-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-[10%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_Teams_TeamTSkills__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    technical_skills: technical_skills,\n                                    role_ids: roleIds && roleIds\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 712,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"SOC Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 721,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 720,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 719,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 726,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 725,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 732,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 731,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 738,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 744,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 743,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight] p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 750,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 749,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 756,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 755,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 762,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 761,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 768,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 767,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 774,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 773,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 780,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 779,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 786,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 785,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 792,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 791,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Senior Cyber Operations Analyst\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 801,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 800,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 799,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 808,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 807,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 814,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 813,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 820,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 819,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 826,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 825,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 832,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 831,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 838,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 837,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 844,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 843,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 850,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 849,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 856,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 855,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 862,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 861,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 868,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 867,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 874,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 873,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Cyber Operations Analyst\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 883,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 882,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 881,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 890,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 896,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 895,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 902,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 908,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 907,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 914,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 913,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 920,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 919,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 926,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 925,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 932,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 931,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 938,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 937,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 944,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 943,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 950,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 949,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 956,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 955,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Lead Cyber Security Engineer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 965,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 963,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 972,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 971,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 978,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 977,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 984,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 983,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 990,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 989,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 996,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 995,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1002,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1001,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1008,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1007,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1014,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1013,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1020,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1019,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tigh p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1026,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1025,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1032,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1031,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1038,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1037,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Cyber Security Engineer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1047,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1046,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1045,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1054,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1053,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1060,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1059,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight  p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1066,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1065,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1072,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1071,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1078,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1077,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1084,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1083,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1090,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1089,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1096,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1095,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1102,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1101,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1108,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1114,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1113,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1120,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Security Business Partner\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1131,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1128,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1127,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1138,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1144,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1150,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1149,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1156,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1155,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1162,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1161,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1168,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1167,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1174,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1180,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1186,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1185,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1192,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1191,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1198,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1197,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1204,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1203,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"Senior PKI Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1213,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1212,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1211,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1220,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1226,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1225,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1232,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1231,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#64C7E9]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1238,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1237,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight] text-black\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1244,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1243,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1250,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1249,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1256,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1255,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1262,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1261,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1268,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1274,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1280,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1286,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1285,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8  bg-[#1f144a] text-primary-foreground rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"font-bold text-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block align-middle\",\n                                            children: \"SMKI RA Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 1295,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1294,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1293,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1302,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1301,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1308,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1307,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1314,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1320,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Supported Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1326,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1325,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1332,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1331,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Independent Practitioner\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1338,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1337,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1344,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1343,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1350,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1349,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1356,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1355,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1362,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1361,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        className: \"text-xs text-center tracking-tight p-0\",\n                                        children: \"Knowledge\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                        lineNumber: 1368,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 1367,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                            lineNumber: 709,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 706,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n        lineNumber: 387,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"0KAjrXij38ScTMtMjYFGziBwE04=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/teams.js\n"));

/***/ })

});