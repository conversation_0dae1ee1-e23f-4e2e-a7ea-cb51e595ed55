"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/teams",{

/***/ "(pages-dir-browser)/./core/Teams/TeamTSkills.js":
/*!***********************************!*\
  !*** ./core/Teams/TeamTSkills.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @core/components/ui/card */ \"(pages-dir-browser)/./core/components/ui/card.jsx\");\n\n\nconst TeamTSkills = (param)=>{\n    let { technical_skills, role_ids } = param;\n    console.log(\"technical_skills\");\n    console.log(technical_skills);\n    console.log(\"role_ids\");\n    console.log(role_ids);\n    /*\n\n    - role ids for the selected team (extract and save in array)\n    - query role mapping for the role ids (multi query)\n    - get the technical skills for the role mappings\n\n\n ----- \n\n */ return technical_skills.map((skill)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n            className: \"min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                className: \"font-extrabold text-lg text-center\",\n                children: \"Skills\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined);\n        const hasMatchingRole = skill.role_mapping.some((mapping)=>role_ids.some((role)=>role.id === mapping.role_id));\n        hasMatchingRole ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n            className: \"min-w-8 bg-[#009cbb] text-primary-foreground\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                className: \"font-semibold text-xs text-center p-1\",\n                children: skill.skill_name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, undefined)\n        }, skill.id, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/core/Teams/TeamTSkills.js\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined) : null;\n    });\n//   const getLevel = (lvl) => {\n//     switch (lvl) {\n//       case 1:\n//         return \"Operational Contributor\";\n//       case 2:\n//         return \"Advanced Contributor\";\n//       case 3:\n//         return \"Team Leader\";\n//       case 4:\n//         return \"Leader of Leaders\";\n//       case 5:\n//         return \"Organisational Leader\";\n//     }\n//   };\n//   return (\n//     <>\n//       {\" \"}\n//       <Card className=\"min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-extrabold text-lg text-center\"}>\n//           Skills\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           BCDR\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Change Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Certification Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold  text-xs text-center p-1\"}>\n//           Compliance & Regulatory Assurance\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold  text-xs text-center p-1\"}>\n//           Data Analytics and Insights\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Development Lifecycle\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Incident Response Lifecycle\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Infrastructure and Cloud Computing\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold  text-xs text-center p-1\"}>\n//           Procurement\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Risk Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Supplier Management\n//         </CardContent>\n//       </Card>\n//       <Card className=\"min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl\">\n//         <CardContent className={\"font-semibold text-xs text-center p-1\"}>\n//           Threat Intelligence\n//         </CardContent>\n//       </Card>\n//     </>\n//   );\n//     filteredBSkills &&\n//     filteredBSkills.map((role) => {\n//       if (role.role_name === \"Team Owner\") {\n//         return null;\n//       }\n//       return (\n//         <>\n//           <Card className=\"min-w-8  bg-[#1f144a] text-primary-foreground \">\n//             <CardContent className={\"font-semibold text-sm text-center\"}>\n//               <div className=\"inline-block align-middle\">{role.role_name}</div>\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight] \">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//           <Card className=\"min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white\">\n//             <CardContent className=\"text-sm text-center tracking-tight\">\n//               {getLevel(role.behavioural_skill_level)}\n//             </CardContent>\n//           </Card>\n//         </>\n//       );\n//     })\n//   );\n};\n_c = TeamTSkills;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TeamTSkills);\nvar _c;\n$RefreshReg$(_c, \"TeamTSkills\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./core/Teams/TeamTSkills.js\n"));

/***/ })

});